#!/usr/bin/env python3
"""
最小化測試腳本
逐步測試每個導入，找出導致卡住的原因
"""

import sys
import os

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import(module_name, import_statement):
    """測試單個模組導入"""
    print(f"🔍 測試導入: {module_name}")
    try:
        exec(import_statement)
        print(f"✅ 成功: {module_name}")
        return True
    except Exception as e:
        print(f"❌ 失敗: {module_name} - {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("最小化測試開始")
    print("=" * 40)
    
    # 逐步測試導入
    imports_to_test = [
        ("tkinter", "import tkinter as tk"),
        ("ttk", "from tkinter import ttk"),
        ("logging", "import logging"),
        ("enhanced_logger", "from utils.enhanced_logger import enhanced_logger"),
        ("config", "from utils.config import Config"),
        ("environment_config", "from utils.environment_config import env_config"),
        ("ip_switcher", "from utils.ip_switcher import ip_switcher"),
        ("ip_switcher_panel", "from views.ip_switcher_panel import IPSwitcherPanel"),
        ("ip_switcher_controller", "from controllers.ip_switcher_controller import IPSwitcherController"),
        ("main_window", "from views.main_window import MainWindow"),
    ]
    
    failed_imports = []
    
    for module_name, import_statement in imports_to_test:
        if not test_import(module_name, import_statement):
            failed_imports.append(module_name)
            print(f"⚠️ 停止測試，因為 {module_name} 導入失敗")
            break
        print()  # 空行分隔
    
    print("=" * 40)
    if failed_imports:
        print(f"❌ 失敗的導入: {', '.join(failed_imports)}")
    else:
        print("✅ 所有導入測試通過")
        
        # 如果所有導入都成功，測試基本功能
        print("\n🔧 測試基本功能...")
        test_basic_functionality()

def test_basic_functionality():
    """測試基本功能"""
    try:
        print("   - 測試創建根視窗...")
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()
        print("   ✅ 根視窗創建成功")
        
        print("   - 測試配置載入...")
        from utils.config import Config
        config = Config()
        print("   ✅ 配置載入成功")
        
        print("   - 測試環境配置...")
        from utils.environment_config import env_config
        current_env = env_config.get_current_environment()
        print(f"   ✅ 當前環境: {current_env}")
        
        print("   - 測試 IP 切換面板創建...")
        from views.ip_switcher_panel import IPSwitcherPanel
        panel = IPSwitcherPanel(root)
        print("   ✅ IP 切換面板創建成功")
        
        print("   - 測試 IP 切換控制器創建...")
        from controllers.ip_switcher_controller import IPSwitcherController
        controller = IPSwitcherController(panel)
        print("   ✅ IP 切換控制器創建成功")
        
        root.destroy()
        print("   ✅ 清理完成")
        
        print("\n🎉 所有基本功能測試通過！")
        
    except Exception as e:
        print(f"   ❌ 基本功能測試失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
