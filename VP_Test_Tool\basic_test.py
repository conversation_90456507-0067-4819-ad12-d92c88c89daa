#!/usr/bin/env python3
"""
基本測試腳本
不添加任何路徑，直接測試
"""

print("基本測試開始")

try:
    print("測試 1: 基本 Python 功能")
    import sys
    print(f"Python 版本: {sys.version}")
    
    print("測試 2: 基本模組導入")
    import os
    print(f"當前目錄: {os.getcwd()}")
    
    print("測試 3: tkinter 導入")
    import tkinter as tk
    print("tkinter 導入成功")
    
    print("測試 4: 創建簡單視窗")
    root = tk.Tk()
    root.withdraw()
    print("簡單視窗創建成功")
    root.destroy()
    
    print("測試 5: 添加專案路徑")
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    print(f"專案路徑: {os.path.dirname(os.path.abspath(__file__))}")
    
    print("測試 6: 導入專案模組")
    try:
        from utils.enhanced_logger import enhanced_logger
        print("enhanced_logger 導入成功")
    except Exception as e:
        print(f"enhanced_logger 導入失敗: {e}")
    
    try:
        from utils.config import Config
        print("Config 導入成功")
    except Exception as e:
        print(f"Config 導入失敗: {e}")
    
    print("所有測試完成")
    
except Exception as e:
    print(f"測試失敗: {e}")
    import traceback
    traceback.print_exc()

print("腳本結束")
