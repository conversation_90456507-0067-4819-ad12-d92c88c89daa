from PIL import Image, ImageDraw
import os

# 創建一個 256x256 的透明背景圖像
icon = Image.new('RGBA', (256, 256), (0, 0, 0, 0))
draw = ImageDraw.Draw(icon)

# 繪製一個簡單的老虎機圖示
# 繪製主體
draw.rectangle((48, 48, 208, 208), fill=(50, 50, 50, 255), outline=(100, 100, 100, 255), width=4)
# 繪製頂部
draw.rectangle((64, 24, 192, 48), fill=(70, 70, 70, 255), outline=(100, 100, 100, 255), width=2)
# 繪製底部
draw.rectangle((64, 208, 192, 232), fill=(70, 70, 70, 255), outline=(100, 100, 100, 255), width=2)
# 繪製顯示窗口
draw.rectangle((64, 64, 192, 144), fill=(200, 200, 200, 255), outline=(150, 150, 150, 255), width=2)
# 繪製三個轉輪
draw.rectangle((72, 72, 112, 136), fill=(255, 100, 100, 255), outline=(200, 50, 50, 255), width=2)
draw.rectangle((118, 72, 158, 136), fill=(100, 255, 100, 255), outline=(50, 200, 50, 255), width=2)
draw.rectangle((164, 72, 184, 136), fill=(100, 100, 255, 255), outline=(50, 50, 200, 255), width=2)
# 繪製拉桿
draw.rectangle((208, 96, 224, 176), fill=(150, 150, 150, 255), outline=(100, 100, 100, 255), width=2)
draw.ellipse((204, 176, 228, 200), fill=(255, 0, 0, 255), outline=(150, 0, 0, 255), width=2)
# 繪製按鈕
draw.ellipse((80, 160, 110, 190), fill=(255, 255, 0, 255), outline=(200, 200, 0, 255), width=2)
draw.ellipse((120, 160, 150, 190), fill=(0, 255, 255, 255), outline=(0, 200, 200, 255), width=2)
draw.ellipse((160, 160, 190, 190), fill=(255, 0, 255, 255), outline=(200, 0, 200, 255), width=2)

# 保存為 ICO 文件
icon.save(os.path.join(os.path.dirname(__file__), 'vp_test_tool.ico'), format='ICO')
print("圖示已創建：vp_test_tool.ico")
