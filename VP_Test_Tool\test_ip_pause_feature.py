#!/usr/bin/env python3
"""
測試 IP 配置暫停功能
"""

import sys
import os
import logging

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_should_pause_function():
    """測試 _should_pause_for_ip_config 函數"""
    print("=== 測試 _should_pause_for_ip_config 函數 ===")
    
    try:
        # 導入主程式中的函數
        from main import _should_pause_for_ip_config
        
        print("🔍 執行 IP 配置檢查...")
        should_pause = _should_pause_for_ip_config()
        
        if should_pause:
            print("✅ 檢測到需要暫停啟動進行 IP 配置")
            return True
        else:
            print("ℹ️ 當前 IP 配置正常，不需要暫停")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pause_dialog():
    """測試暫停對話框"""
    print("\n=== 測試暫停對話框 ===")
    
    try:
        import tkinter as tk
        from main import _pause_startup_for_ip_config
        
        # 創建測試根視窗
        root = tk.Tk()
        root.title("測試根視窗")
        root.geometry("400x300")
        
        # 創建模擬主視窗
        main_window = tk.Frame(root)
        main_window.pack(fill=tk.BOTH, expand=True)
        
        print("🔍 顯示 IP 配置對話框...")
        print("請在對話框中測試 IP 配置功能")
        
        # 調用暫停對話框
        result = _pause_startup_for_ip_config(root, main_window)
        
        if result:
            print("✅ 用戶選擇繼續啟動")
        else:
            print("ℹ️ 用戶選擇退出程式")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ip_config_check():
    """測試 IP 配置檢查邏輯"""
    print("\n=== 測試 IP 配置檢查邏輯 ===")
    
    try:
        from utils.environment_config import env_config
        import requests
        
        # 取得當前環境配置
        current_env = env_config.get_current_environment()
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        
        print(f"當前環境: {current_env}")
        print(f"API 伺服器數量: {len(api_servers)}")
        
        # 檢查關鍵服務
        key_services = ["mysql_operator", "gamebridge"]
        test_urls = []
        
        for service in key_services:
            if service in api_servers:
                test_urls.append((service, api_servers[service]))
                print(f"找到關鍵服務: {service} - {api_servers[service]}")
        
        if not test_urls:
            print("⚠️ 沒有找到關鍵服務配置")
            return True
        
        # 快速連接測試
        failed_connections = 0
        for service, url in test_urls:
            try:
                print(f"🔍 測試連接: {service} - {url}")
                response = requests.get(url, timeout=1)
                print(f"✅ 連接成功: {service}")
            except Exception as e:
                print(f"❌ 連接失敗: {service} - {e}")
                failed_connections += 1
        
        # 判斷是否需要暫停
        should_pause = failed_connections >= len(test_urls) / 2
        
        print(f"\n連接結果: {failed_connections}/{len(test_urls)} 個服務連接失敗")
        print(f"是否需要暫停: {'是' if should_pause else '否'}")
        
        return should_pause
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_config():
    """測試環境配置"""
    print("\n=== 測試環境配置 ===")
    
    try:
        from utils.environment_config import env_config
        
        # 檢查當前環境
        current_env = env_config.get_current_environment()
        print(f"當前環境: {current_env}")
        
        # 檢查環境資訊
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        
        print("API 伺服器配置:")
        for service, url in api_servers.items():
            print(f"  {service}: {url}")
        
        if api_servers:
            print("✅ 環境配置正常")
            return True
        else:
            print("❌ 沒有找到 API 伺服器配置")
            return False
            
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("IP 配置暫停功能測試開始")
    print("=" * 50)
    
    # 設定日誌級別
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_environment_config,
        test_ip_config_check,
        test_should_pause_function,
        test_pause_dialog  # 這個會顯示對話框
    ]
    
    passed = 0
    total = len(tests)
    
    for i, test in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test.__name__}")
        print("-" * 40)
        try:
            if test():
                passed += 1
                print(f"✅ 測試 {test.__name__} 通過")
            else:
                print(f"ℹ️ 測試 {test.__name__} 完成（結果可能因環境而異）")
                passed += 1  # 對於這類測試，完成即算通過
        except Exception as e:
            print(f"❌ 測試 {test.__name__} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 IP 配置暫停功能測試完成！")
        return 0
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
