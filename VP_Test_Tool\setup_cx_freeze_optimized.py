"""
使用 cx_Freeze 打包 VP Test Tool 為 exe 文件
優化版本，解決啟動無回應問題
"""
import sys
import os
from cx_Freeze import setup, Executable

# 應用程式信息
APP_NAME = "VP_Test_Tool"
APP_VERSION = "2.6.2"
APP_DESCRIPTION = "VP Test Tool"
APP_AUTHOR = "VP Test Tool Team"

# 圖示路徑
icon_path = os.path.abspath(os.path.join("assets", "icons", "vp_test_tool.ico"))
if not os.path.exists(icon_path):
    # 嘗試使用備用圖示
    backup_icon_path = os.path.abspath(os.path.join("assets", "app_icon.ico"))
    if os.path.exists(backup_icon_path):
        print(f"Using backup icon at {backup_icon_path}")
        icon_path = backup_icon_path
    else:
        print(f"Warning: Icon file not found at {icon_path} or {backup_icon_path}")
        icon_path = None

# 基本設置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 使用 GUI 模式，不顯示控制台窗口

# 包含的文件和目錄
include_files = [
    ("assets", "assets"),  # 包含資源文件
    ("config.json", "config.json"),  # 包含配置文件
    ("CHANGELOG.md", "CHANGELOG.md"),  # 包含更新日誌
]

# 包含的包
packages = [
    "tkinter",
    "PIL",
    "utils",
    "views",
    "models",
    "controllers",
    "widgets",
    "json",
    "logging",
    "requests",
    "urllib3",
    "idna",  # requests 依賴
    "certifi",  # requests 依賴
    "chardet",  # requests 依賴
    "charset_normalizer",  # requests 依賴
    "queue",  # 用於多線程處理
    "threading",  # 用於多線程處理
    "concurrent",  # 用於並發處理
    "concurrent.futures",  # 用於線程池
    "functools",  # 用於函數工具
    "datetime",  # 用於日期時間處理
    "random",  # 用於隨機數生成
    "string",  # 用於字符串處理
    "os",  # 用於操作系統相關功能
    "sys",  # 用於系統相關功能
    "traceback",  # 用於錯誤追蹤
    "ssl",  # 用於 SSL 連接
    "socket",  # 用於網絡連接
    "time",  # 用於時間相關功能
    "http",  # 用於 HTTP 連接
    "http.client",  # 用於 HTTP 客戶端
    "http.cookiejar",  # 用於 Cookie 處理
    "http.cookies",  # 用於 Cookie 處理
    "email",  # requests 依賴
    "xml",  # 用於 XML 處理
    "xml.etree",  # 用於 XML 處理
    "xml.etree.ElementTree",  # 用於 XML 處理
    "html",  # 用於 HTML 處理
    "html.parser",  # 用於 HTML 解析
    "csv",  # 用於 CSV 文件處理
    "subprocess",  # 用於執行子進程
    "tempfile",  # 用於臨時文件處理
    "urllib",  # 用於 URL 處理
    "urllib.parse",  # 用於 URL 解析
    "urllib.request",  # 用於 URL 請求
    "pandas",  # 用於數據處理和 Excel 文件讀取
    "numpy",  # pandas 依賴
    "openpyxl",  # 用於 Excel 文件處理
    "xlrd",  # 用於舊版 Excel 文件處理
]

# 包含的模組
includes = [
    "tkinter.ttk",
    "tkinter.messagebox",
    "tkinter.filedialog",
    "PIL.Image",
    "PIL.ImageTk",
]

# 排除的包
excludes = [
    "unittest",
    "pydoc",
    "doctest",
    "argparse",
    "pdb",
    "scipy",
    "matplotlib",
    "PyQt5",
    "PyQt6",
    "PySide2",
    "PySide6",
    "IPython",
    "jupyter",
    "notebook",
    "test",
    "distutils",
]

# 構建選項
build_options = {
    "packages": packages,
    "includes": includes,
    "excludes": excludes,
    "include_files": include_files,
    "build_exe": "dist/cx_freeze_optimized",  # 輸出目錄
    "optimize": 1,  # 降低優化級別，避免某些模組被移除
    "include_msvcr": True,  # 包含 MSVC 運行時
    "silent": False,  # 顯示詳細輸出
    "zip_include_packages": "*",  # 包含所有包
    "zip_exclude_packages": "",  # 不排除任何包
}

# 可執行文件選項
executables = [
    Executable(
        "main.py",  # 主程序文件
        base=base,
        target_name=APP_NAME,  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=APP_NAME,  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加診斷模式可執行文件
    Executable(
        "debug_startup.py",  # 診斷啟動腳本
        base=base,
        target_name=f"{APP_NAME}_Diagnostic",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Diagnostic",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加網絡測試模式可執行文件
    Executable(
        "network_test.py",  # 網絡測試腳本
        base=base,
        target_name=f"{APP_NAME}_NetworkTest",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Network Test",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加簡單模式可執行文件
    Executable(
        "simple_startup.py",  # 簡單啟動腳本
        base=base,
        target_name=f"{APP_NAME}_Simple",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Simple",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    )
]

# 設置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_options},
    executables=executables
)

print(f"打包完成！exe 文件位於: {os.path.abspath(os.path.join('dist', 'cx_freeze_optimized', APP_NAME + '.exe'))}")
print(f"診斷模式 exe 文件位於: {os.path.abspath(os.path.join('dist', 'cx_freeze_optimized', APP_NAME + '_Diagnostic.exe'))}")
