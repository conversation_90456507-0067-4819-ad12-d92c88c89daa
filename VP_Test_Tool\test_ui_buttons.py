#!/usr/bin/env python3
"""
測試 UI 按鈕樣式修復
驗證 API IP 切換工具的按鈕是否顯示正確
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ip_switcher_panel():
    """測試 IP 切換面板的按鈕樣式"""
    print("=== 測試 IP 切換面板按鈕樣式 ===")
    
    try:
        # 創建測試視窗
        root = tk.Tk()
        root.title("IP 切換工具按鈕測試")
        root.geometry("1200x800")
        
        # 導入 IP 切換面板
        from views.ip_switcher_panel import IPSwitcherPanel
        
        print("🔍 創建 IP 切換面板...")
        ip_panel = IPSwitcherPanel(root)
        ip_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # 檢查按鈕是否存在並有正確樣式
        buttons_to_check = [
            ("快速切換按鈕", "switch_btn"),
            ("應用模板按鈕", "apply_template_btn"),
            ("新增模板按鈕", "add_template_btn"),
            ("編輯模板按鈕", "edit_template_btn"),
            ("刪除模板按鈕", "delete_template_btn"),
            ("編輯 URL 按鈕", "edit_url_btn"),
            ("保存配置按鈕", "save_urls_btn"),
            ("重置配置按鈕", "reset_urls_btn"),
            ("重新載入按鈕", "reload_urls_btn"),
            ("恢復配置按鈕", "restore_btn"),
            ("清除歷史按鈕", "clear_history_btn"),
            ("重新整理按鈕", "refresh_btn")
        ]
        
        print("\n檢查按鈕樣式:")
        all_buttons_ok = True
        
        for button_name, button_attr in buttons_to_check:
            if hasattr(ip_panel, button_attr):
                button = getattr(ip_panel, button_attr)
                
                # 檢查是否是 tk.Button（而不是 ttk.Button）
                if isinstance(button, tk.Button):
                    # 檢查按鈕樣式
                    bg_color = button.cget("bg")
                    fg_color = button.cget("fg")
                    font = button.cget("font")
                    relief = button.cget("relief")
                    
                    print(f"✅ {button_name}: 樣式正確")
                    print(f"   背景色: {bg_color}")
                    print(f"   文字色: {fg_color}")
                    print(f"   字體: {font}")
                    print(f"   邊框: {relief}")
                else:
                    print(f"❌ {button_name}: 仍使用 ttk.Button")
                    all_buttons_ok = False
            else:
                print(f"❌ {button_name}: 按鈕不存在")
                all_buttons_ok = False
            print()
        
        if all_buttons_ok:
            print("🎉 所有按鈕樣式都正確！")
        else:
            print("⚠️ 部分按鈕樣式需要修復")
        
        # 添加說明標籤
        info_label = tk.Label(
            root,
            text="請檢查所有按鈕是否清晰可見，具有明顯的按鈕外觀",
            font=("Microsoft JhengHei UI", 12),
            fg="blue"
        )
        info_label.pack(pady=10)
        
        # 添加關閉按鈕
        close_btn = tk.Button(
            root,
            text="關閉測試",
            command=root.destroy,
            bg="#B71C1C",  # 深紅色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 12, "bold"),
            activebackground="#D32F2F",  # 中紅色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=20,
            pady=10,
            cursor="hand2"
        )
        close_btn.pack(pady=10)
        
        print("顯示測試視窗，請檢查按鈕外觀...")
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pause_dialog_buttons():
    """測試暫停對話框的按鈕樣式"""
    print("\n=== 測試暫停對話框按鈕樣式 ===")
    
    try:
        # 創建測試視窗
        root = tk.Tk()
        root.title("暫停對話框按鈕測試")
        root.geometry("800x600")
        
        # 創建模擬的暫停對話框
        dialog = tk.Toplevel(root)
        dialog.title("API IP 配置 - 按鈕測試")
        dialog.geometry("600x400")
        dialog.transient(root)
        
        # 居中顯示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 創建說明標籤
        info_frame = ttk.Frame(dialog)
        info_frame.pack(fill=tk.X, padx=20, pady=20)
        
        title_label = ttk.Label(
            info_frame,
            text="⚠️ 暫停對話框按鈕樣式測試",
            font=("Microsoft JhengHei UI", 14, "bold"),
            foreground="red"
        )
        title_label.pack()
        
        info_label = ttk.Label(
            info_frame,
            text="請檢查下方按鈕是否具有明顯的按鈕外觀",
            font=("Microsoft JhengHei UI", 10),
            justify=tk.CENTER
        )
        info_label.pack(pady=10)
        
        # 創建按鈕框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # 測試連接按鈕
        test_btn = tk.Button(
            button_frame,
            text="測試連接",
            bg="#01579B",  # 深淺藍色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#0277BD",  # 中淺藍色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        test_btn.pack(side=tk.LEFT, padx=8)
        
        # 繼續啟動按鈕
        continue_btn = tk.Button(
            button_frame,
            text="繼續啟動",
            bg="#1B5E20",  # 深綠色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#2E7D32",  # 中綠色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        continue_btn.pack(side=tk.RIGHT, padx=8)
        
        # 退出程式按鈕
        exit_btn = tk.Button(
            button_frame,
            text="退出程式",
            bg="#B71C1C",  # 深紅色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#D32F2F",  # 中紅色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        exit_btn.pack(side=tk.RIGHT, padx=8)
        
        # 添加關閉按鈕
        close_btn = tk.Button(
            dialog,
            text="關閉測試",
            command=lambda: [dialog.destroy(), root.destroy()],
            bg="#424242",  # 深灰色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 12, "bold"),
            activebackground="#616161",  # 中灰色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=20,
            pady=10,
            cursor="hand2"
        )
        close_btn.pack(pady=20)
        
        print("✅ 暫停對話框按鈕樣式測試創建成功")
        print("顯示測試對話框，請檢查按鈕外觀...")
        
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("UI 按鈕樣式測試開始")
    print("=" * 50)
    
    tests = [
        ("IP 切換面板按鈕測試", test_ip_switcher_panel),
        ("暫停對話框按鈕測試", test_pause_dialog_buttons)
    ]
    
    passed = 0
    total = len(tests)
    
    for i, (test_name, test_func) in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test_name}")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ 測試 {test_name} 通過")
            else:
                print(f"❌ 測試 {test_name} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test_name} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有 UI 按鈕樣式測試通過！")
        print("\n主要改進:")
        print("✅ 所有按鈕使用 tk.Button 而非 ttk.Button")
        print("✅ 按鈕具有明顯的顏色和邊框")
        print("✅ 按鈕有懸停效果和手型游標")
        print("✅ 按鈕文字使用粗體字型")
        print("✅ 按鈕間距適當，佈局美觀")
        return 0
    else:
        print("⚠️ 部分測試未通過，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
