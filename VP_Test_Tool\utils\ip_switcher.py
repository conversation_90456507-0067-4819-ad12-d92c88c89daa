"""快速 IP 切換工具

提供快速切換常用 IP 地址的功能，支援：
1. 預設 IP 模板
2. 快速切換
3. IP 歷史記錄
4. 批量更新
"""

import json
import os
import logging
from typing import Dict, List, Optional
from datetime import datetime
from .environment_config import env_config

logger = logging.getLogger(__name__)

class IPSwitcher:
    """快速 IP 切換工具"""

    def __init__(self):
        self.history_file = "ip_history.json"
        self.templates_file = "ip_templates.json"
        self.ip_history = []
        self.ip_templates = {}

        self.load_history()
        self.load_templates()

    def load_history(self):
        """載入 IP 歷史記錄"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.ip_history = json.load(f)
            else:
                self.ip_history = []
        except Exception as e:
            logger.error(f"載入 IP 歷史記錄失敗: {e}")
            self.ip_history = []

    def save_history(self):
        """儲存 IP 歷史記錄"""
        try:
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(self.ip_history, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"儲存 IP 歷史記錄失敗: {e}")

    def load_templates(self):
        """載入 IP 模板"""
        try:
            if os.path.exists(self.templates_file):
                with open(self.templates_file, 'r', encoding='utf-8') as f:
                    self.ip_templates = json.load(f)
            else:
                self.create_default_templates()
                self.save_templates()
        except Exception as e:
            logger.error(f"載入 IP 模板失敗: {e}")
            self.create_default_templates()

    def save_templates(self):
        """儲存 IP 模板"""
        try:
            with open(self.templates_file, 'w', encoding='utf-8') as f:
                json.dump(self.ip_templates, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"儲存 IP 模板失敗: {e}")

    def create_default_templates(self):
        """創建預設 IP 模板"""
        self.ip_templates = {
            "常用開發環境": {
                "description": "常用的開發環境 IP 配置",
                "mysql_operator": "http://************:5000",
                "gamebridge": "http://gamebridge:8080",
                "tokenguard": "https://gp001-qa1-tokenguard.xwautc.online",
                "lottery": "http://lottery:8080",
                "simulation": "http://simulationweb-go:8080"
            },
            "舊版測試環境": {
                "description": "舊版測試環境 IP 配置",
                "mysql_operator": "http://************:5000",
                "gamebridge": "http://gamebridge:8080",
                "tokenguard": "https://gp001-qa1-tokenguard.xwautc.online",
                "lottery": "http://lottery:8080",
                "simulation": "http://simulationweb-go:8080"
            },
            "本地開發環境": {
                "description": "本地開發環境 IP 配置",
                "mysql_operator": "http://localhost:5000",
                "gamebridge": "http://localhost:8080",
                "tokenguard": "https://localhost:8443",
                "lottery": "http://localhost:8081",
                "simulation": "http://localhost:8082"
            },
            "生產環境": {
                "description": "生產環境 IP 配置",
                "mysql_operator": "http://*************:5000",
                "gamebridge": "http://prod-gamebridge:8080",
                "tokenguard": "https://prod-tokenguard.xwautc.online",
                "lottery": "http://prod-lottery:8080",
                "simulation": "http://prod-simulation:8080"
            }
        }

    def get_templates(self) -> Dict[str, Dict[str, str]]:
        """取得所有 IP 模板"""
        return self.ip_templates

    def get_template(self, template_name: str) -> Optional[Dict[str, str]]:
        """取得指定的 IP 模板"""
        return self.ip_templates.get(template_name)

    def add_template(self, template_name: str, template_data: Dict[str, str], description: str = "") -> bool:
        """新增 IP 模板"""
        try:
            self.ip_templates[template_name] = {
                "description": description,
                **template_data
            }
            self.save_templates()
            logger.info(f"已新增 IP 模板: {template_name}")
            return True
        except Exception as e:
            logger.error(f"新增 IP 模板失敗: {e}")
            return False

    def remove_template(self, template_name: str) -> bool:
        """移除 IP 模板"""
        try:
            if template_name in self.ip_templates:
                del self.ip_templates[template_name]
                self.save_templates()
                logger.info(f"已移除 IP 模板: {template_name}")
                return True
            else:
                logger.warning(f"IP 模板不存在: {template_name}")
                return False
        except Exception as e:
            logger.error(f"移除 IP 模板失敗: {e}")
            return False

    def apply_template(self, template_name: str, env_name: str = None) -> bool:
        """應用 IP 模板到指定環境"""
        try:
            template = self.get_template(template_name)
            if not template:
                logger.error(f"IP 模板不存在: {template_name}")
                return False

            # 移除描述欄位，只保留伺服器配置
            server_config = {k: v for k, v in template.items() if k != "description"}

            # 記錄到歷史
            self.add_to_history(template_name, server_config)

            # 應用到環境配置
            env_name = env_name or env_config.get_current_environment()

            # 批量更新伺服器配置
            for server_type, url in server_config.items():
                success = env_config.update_api_server(server_type, url, env_name)
                if not success:
                    logger.error(f"更新 {server_type} 失敗")
                    return False

            logger.info(f"已應用 IP 模板 '{template_name}' 到環境 '{env_name}'")
            return True

        except Exception as e:
            logger.error(f"應用 IP 模板失敗: {e}")
            return False

    def quick_switch_ip(self, old_ip: str, new_ip: str, env_name: str = None) -> bool:
        """快速切換 IP（將所有包含舊 IP 的 URL 替換為新 IP）"""
        try:
            env_name = env_name or env_config.get_current_environment()
            env_info = env_config.get_environment_info(env_name)
            api_servers = env_info.get("api_servers", {})

            updated_servers = {}
            changed_count = 0

            # 替換所有包含舊 IP 的 URL
            for server_type, url in api_servers.items():
                if old_ip in url:
                    new_url = url.replace(old_ip, new_ip)
                    updated_servers[server_type] = new_url
                    changed_count += 1
                else:
                    updated_servers[server_type] = url

            if changed_count == 0:
                logger.warning(f"在環境 '{env_name}' 中找不到包含 IP '{old_ip}' 的配置")
                return False

            # 批量更新
            for server_type, url in updated_servers.items():
                if server_type in api_servers and api_servers[server_type] != url:
                    success = env_config.update_api_server(server_type, url, env_name)
                    if not success:
                        logger.error(f"更新 {server_type} 失敗")
                        return False

            # 記錄到歷史
            self.add_to_history(f"IP切換: {old_ip} -> {new_ip}", updated_servers)

            logger.info(f"已將環境 '{env_name}' 中的 IP '{old_ip}' 切換為 '{new_ip}'，共更新 {changed_count} 個配置")
            return True

        except Exception as e:
            logger.error(f"快速切換 IP 失敗: {e}")
            return False

    def add_to_history(self, operation: str, server_config: Dict[str, str]):
        """新增到歷史記錄"""
        try:
            history_entry = {
                "timestamp": datetime.now().isoformat(),
                "operation": operation,
                "server_config": server_config
            }

            self.ip_history.insert(0, history_entry)

            # 限制歷史記錄數量
            if len(self.ip_history) > 50:
                self.ip_history = self.ip_history[:50]

            self.save_history()

        except Exception as e:
            logger.error(f"新增歷史記錄失敗: {e}")

    def add_history(self, operation: str, server_config: Dict[str, str]):
        """新增到歷史記錄（別名方法）"""
        self.add_to_history(operation, server_config)

    def get_history(self, limit: int = 20) -> List[Dict]:
        """取得歷史記錄"""
        return self.ip_history[:limit]

    def clear_history(self) -> bool:
        """清除歷史記錄"""
        try:
            self.ip_history = []
            self.save_history()
            logger.info("已清除 IP 切換歷史記錄")
            return True
        except Exception as e:
            logger.error(f"清除歷史記錄失敗: {e}")
            return False

    def restore_from_history(self, history_index: int, env_name: str = None) -> bool:
        """從歷史記錄恢復配置"""
        try:
            if history_index < 0 or history_index >= len(self.ip_history):
                logger.error(f"歷史記錄索引無效: {history_index}")
                return False

            history_entry = self.ip_history[history_index]
            server_config = history_entry["server_config"]

            env_name = env_name or env_config.get_current_environment()

            # 批量更新伺服器配置
            for server_type, url in server_config.items():
                success = env_config.update_api_server(server_type, url, env_name)
                if not success:
                    logger.error(f"恢復 {server_type} 失敗")
                    return False

            # 記錄恢復操作
            operation = history_entry["operation"]
            self.add_to_history(f"恢復: {operation}", server_config)

            logger.info(f"已從歷史記錄恢復配置到環境 '{env_name}'")
            return True

        except Exception as e:
            logger.error(f"從歷史記錄恢復失敗: {e}")
            return False

    def get_current_ips(self, env_name: str = None) -> Dict[str, str]:
        """取得當前環境的所有 IP 配置"""
        try:
            env_name = env_name or env_config.get_current_environment()
            env_info = env_config.get_environment_info(env_name)
            api_servers = env_info.get("api_servers", {})

            # 提取 IP 地址
            ips = {}
            for server_type, url in api_servers.items():
                # 簡單的 IP 提取（可能需要更複雜的邏輯）
                import re
                ip_match = re.search(r'(\d+\.\d+\.\d+\.\d+)', url)
                if ip_match:
                    ips[server_type] = ip_match.group(1)
                else:
                    # 如果不是 IP，保留原始 URL
                    ips[server_type] = url

            return ips

        except Exception as e:
            logger.error(f"取得當前 IP 配置失敗: {e}")
            return {}

    def find_common_ips(self, env_name: str = None) -> List[str]:
        """找出當前環境中的常見 IP 地址"""
        try:
            current_ips = self.get_current_ips(env_name)

            # 統計 IP 出現次數
            ip_count = {}
            for server_type, ip in current_ips.items():
                import re
                if re.match(r'\d+\.\d+\.\d+\.\d+', ip):  # 只統計真正的 IP
                    ip_count[ip] = ip_count.get(ip, 0) + 1

            # 按出現次數排序
            common_ips = sorted(ip_count.keys(), key=lambda x: ip_count[x], reverse=True)

            return common_ips

        except Exception as e:
            logger.error(f"找出常見 IP 失敗: {e}")
            return []

# 全域 IP 切換器實例
ip_switcher = IPSwitcher()
