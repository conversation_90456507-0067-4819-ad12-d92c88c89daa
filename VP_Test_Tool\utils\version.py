"""版本管理模塊

此模塊集中管理應用程式的版本信息，所有需要使用版本號的地方都應該從這裡導入。
"""

# 主版本號
MAJOR = 2
# 次版本號
MINOR = 6
# 修訂版本號
PATCH = 2

# 完整版本號
VERSION = f"{MAJOR}.{MINOR}.{PATCH}"
# 應用程式標題
APP_TITLE = f"VP Test Tool V{VERSION}"
# 版本發布日期 (YYYY-MM-DD)
RELEASE_DATE = "2025-05-28"

# 版本描述
VERSION_DESCRIPTION = """
VP Test Tool V{VERSION} 版本更新 - API IP 切換工具優化：

🔧 功能修復與優化：
1. API IP 切換工具：
   - 修復頁籤名稱顯示問題，統一為 "API IP 切換工具"
   - 修復控制器初始化問題，確保功能正常運作
   - 移除多餘的 network 圖示，簡化界面顯示
   - 完善錯誤處理和日誌記錄機制

🧪 測試與驗證：
2. 新增完整測試套件：
   - 基本功能測試：驗證模組導入和基礎功能
   - 操作功能測試：驗證 IP 切換邏輯和模板應用
   - 配置驗證測試：確保環境配置的正確性
   - 歷史記錄測試：驗證操作歷史管理功能

📋 除錯與診斷：
3. 除錯工具改進：
   - 新增詳細的除錯報告生成功能
   - 提供完整的功能狀態檢查
   - 改進錯誤診斷和問題定位能力
   - 新增自動化測試腳本

🖥️ UI 改進：
4. 界面優化：
   - 統一頁籤命名規範
   - 優化導航提示文字
   - 改進快捷鍵支援 (Ctrl+5)
   - 簡化圖示顯示

📚 文件更新：
5. 新增完整的除錯報告和測試文件
""".format(VERSION=VERSION)

def get_version_info():
    """獲取版本信息

    Returns:
        dict: 包含版本信息的字典
    """
    return {
        "major": MAJOR,
        "minor": MINOR,
        "patch": PATCH,
        "version": VERSION,
        "app_title": APP_TITLE,
        "release_date": RELEASE_DATE,
        "description": VERSION_DESCRIPTION
    }

def is_newer_version(version_to_check):
    """檢查指定的版本是否比當前版本更新

    Args:
        version_to_check (str): 要檢查的版本號，格式為 "x.y.z"

    Returns:
        bool: 如果指定的版本比當前版本更新，則返回 True，否則返回 False
    """
    try:
        # 解析版本號
        major, minor, patch = map(int, version_to_check.split('.'))

        # 比較版本號
        if major > MAJOR:
            return True
        if major == MAJOR and minor > MINOR:
            return True
        if major == MAJOR and minor == MINOR and patch > PATCH:
            return True

        return False
    except:
        # 如果解析失敗，返回 False
        return False

def get_version_string(include_v=True):
    """獲取版本號字符串

    Args:
        include_v (bool): 是否包含 "V" 前綴

    Returns:
        str: 版本號字符串
    """
    if include_v:
        return f"V{VERSION}"
    else:
        return VERSION

if __name__ == "__main__":
    # 測試代碼
    print(f"當前版本: {VERSION}")
    print(f"應用程式標題: {APP_TITLE}")
    print(f"版本發布日期: {RELEASE_DATE}")
    print(f"版本描述: {VERSION_DESCRIPTION}")

    # 測試版本比較
    test_versions = ["2.4.0", "2.5.0", "2.5.1", "2.6.0", "3.0.0"]
    for test_version in test_versions:
        print(f"{test_version} 是否比當前版本更新: {is_newer_version(test_version)}")
