"""快速 IP 切換控制器"""

import tkinter as tk
from tkinter import messagebox, simpledialog, ttk
import logging
from typing import Dict, List
from utils.ip_switcher import ip_switcher
from utils.environment_config import env_config
from views.ip_switcher_panel import IPSwitcherPanel

logger = logging.getLogger(__name__)

class IPSwitcherController:
    """快速 IP 切換控制器"""

    def __init__(self, ip_switcher_panel: IPSwitcherPanel):
        self.view = ip_switcher_panel
        self.ip_switcher = ip_switcher
        self.env_config = env_config

        try:
            # 綁定事件
            self.setup_bindings()

            # 延遲初始化界面，避免啟動時卡住
            self.view.after(100, self.refresh_data)

            logger.info("成功初始化 API IP 切換工具控制器")
        except Exception as e:
            logger.error(f"初始化 API IP 切換工具控制器失敗: {e}")
            raise

    def setup_bindings(self):
        """設置事件綁定"""
        # 綁定方法到視圖
        self.view.quick_switch_ip = self.quick_switch_ip
        self.view.apply_template = self.apply_template
        self.view.add_template = self.add_template
        self.view.edit_template = self.edit_template
        self.view.delete_template = self.delete_template
        self.view.restore_from_history = self.restore_from_history
        self.view.clear_history = self.clear_history
        self.view.refresh_data = self.refresh_data
        self.view.on_template_select = self.on_template_select
        self.view.edit_url = self.edit_url
        self.view.save_urls = self.save_urls
        self.view.reset_urls = self.reset_urls
        self.view.reload_urls = self.reload_urls

        # 重新配置按鈕命令
        self.view.switch_btn.config(command=self.quick_switch_ip)
        self.view.apply_template_btn.config(command=self.apply_template)
        self.view.add_template_btn.config(command=self.add_template)
        self.view.edit_template_btn.config(command=self.edit_template)
        self.view.delete_template_btn.config(command=self.delete_template)
        self.view.restore_btn.config(command=self.restore_from_history)
        self.view.clear_history_btn.config(command=self.clear_history)
        self.view.refresh_btn.config(command=self.refresh_data)
        self.view.edit_url_btn.config(command=self.edit_url)
        self.view.save_urls_btn.config(command=self.save_urls)
        self.view.reset_urls_btn.config(command=self.reset_urls)
        self.view.reload_urls_btn.config(command=self.reload_urls)

    def refresh_data(self):
        """重新整理數據"""
        try:
            # 更新模板列表
            templates = self.ip_switcher.get_templates()
            self.view.update_template_list(templates)

            # 更新歷史記錄
            history = self.ip_switcher.get_history()
            self.view.update_history_list(history)

            # 更新當前配置
            current_env = self.env_config.get_current_environment()
            env_info = self.env_config.get_environment_info(current_env)
            current_config = env_info.get("api_servers", {})
            self.view.update_current_config(current_config)

            # 更新 URL 列表
            self.view.update_url_list(current_config)

            # 更新當前常用 IP
            common_ips = self.ip_switcher.find_common_ips()
            if common_ips:
                self.view.set_current_ip(common_ips[0])
            else:
                self.view.set_current_ip("無")

        except Exception as e:
            logger.error(f"重新整理數據失敗: {e}")
            messagebox.showerror("錯誤", f"重新整理數據失敗: {e}")

    def quick_switch_ip(self):
        """快速切換 IP"""
        try:
            old_ip = self.view.old_ip_var.get().strip()
            new_ip = self.view.new_ip_var.get().strip()

            if not old_ip or not new_ip:
                messagebox.showwarning("警告", "請輸入舊 IP 和新 IP")
                return

            if old_ip == new_ip:
                messagebox.showwarning("警告", "舊 IP 和新 IP 不能相同")
                return

            # 確認切換
            result = messagebox.askyesno(
                "確認切換",
                f"確定要將所有包含 '{old_ip}' 的配置切換為 '{new_ip}' 嗎？"
            )

            if result:
                success = self.ip_switcher.quick_switch_ip(old_ip, new_ip)
                if success:
                    messagebox.showinfo("成功", f"IP 切換成功: {old_ip} -> {new_ip}")
                    self.refresh_data()
                    self._reload_api_urls()
                else:
                    messagebox.showerror("錯誤", "IP 切換失敗，請檢查日誌")

        except Exception as e:
            logger.error(f"快速切換 IP 失敗: {e}")
            messagebox.showerror("錯誤", f"快速切換 IP 失敗: {e}")

    def apply_template(self):
        """應用模板"""
        try:
            template_name = self.view.get_selected_template()
            if not template_name:
                messagebox.showwarning("警告", "請選擇要應用的模板")
                return

            # 確認應用
            result = messagebox.askyesno(
                "確認應用",
                f"確定要應用模板 '{template_name}' 嗎？\n\n"
                "這將覆蓋當前環境的所有伺服器配置。"
            )

            if result:
                success = self.ip_switcher.apply_template(template_name)
                if success:
                    messagebox.showinfo("成功", f"模板 '{template_name}' 應用成功")
                    self.refresh_data()
                    self._reload_api_urls()
                else:
                    messagebox.showerror("錯誤", f"應用模板 '{template_name}' 失敗")

        except Exception as e:
            logger.error(f"應用模板失敗: {e}")
            messagebox.showerror("錯誤", f"應用模板失敗: {e}")

    def add_template(self):
        """新增模板"""
        try:
            # 創建新增模板對話框
            dialog = TemplateDialog(self.view, "新增模板")
            result = dialog.show()

            if result:
                template_name = result["name"]
                description = result["description"]
                server_config = result["server_config"]

                success = self.ip_switcher.add_template(template_name, server_config, description)
                if success:
                    messagebox.showinfo("成功", f"模板 '{template_name}' 新增成功")
                    self.refresh_data()
                else:
                    messagebox.showerror("錯誤", f"新增模板 '{template_name}' 失敗")

        except Exception as e:
            logger.error(f"新增模板失敗: {e}")
            messagebox.showerror("錯誤", f"新增模板失敗: {e}")

    def edit_template(self):
        """編輯模板"""
        try:
            template_name = self.view.get_selected_template()
            if not template_name:
                messagebox.showwarning("警告", "請選擇要編輯的模板")
                return

            # 取得模板資料
            template_data = self.ip_switcher.get_template(template_name)
            if not template_data:
                messagebox.showerror("錯誤", f"找不到模板: {template_name}")
                return

            # 創建編輯模板對話框
            dialog = TemplateDialog(self.view, "編輯模板", template_name, template_data)
            result = dialog.show()

            if result:
                description = result["description"]
                server_config = result["server_config"]

                success = self.ip_switcher.add_template(template_name, server_config, description)
                if success:
                    messagebox.showinfo("成功", f"模板 '{template_name}' 更新成功")
                    self.refresh_data()
                else:
                    messagebox.showerror("錯誤", f"更新模板 '{template_name}' 失敗")

        except Exception as e:
            logger.error(f"編輯模板失敗: {e}")
            messagebox.showerror("錯誤", f"編輯模板失敗: {e}")

    def delete_template(self):
        """刪除模板"""
        try:
            template_name = self.view.get_selected_template()
            if not template_name:
                messagebox.showwarning("警告", "請選擇要刪除的模板")
                return

            # 確認刪除
            result = messagebox.askyesno(
                "確認刪除",
                f"確定要刪除模板 '{template_name}' 嗎？\n\n"
                "此操作無法復原！"
            )

            if result:
                success = self.ip_switcher.remove_template(template_name)
                if success:
                    messagebox.showinfo("成功", f"模板 '{template_name}' 刪除成功")
                    self.refresh_data()
                else:
                    messagebox.showerror("錯誤", f"刪除模板 '{template_name}' 失敗")

        except Exception as e:
            logger.error(f"刪除模板失敗: {e}")
            messagebox.showerror("錯誤", f"刪除模板失敗: {e}")

    def restore_from_history(self):
        """從歷史記錄恢復"""
        try:
            history_index = self.view.get_selected_history_index()
            if history_index < 0:
                messagebox.showwarning("警告", "請選擇要恢復的歷史記錄")
                return

            # 取得歷史記錄
            history = self.ip_switcher.get_history()
            if history_index >= len(history):
                messagebox.showerror("錯誤", "選擇的歷史記錄無效")
                return

            history_entry = history[history_index]
            operation = history_entry.get("operation", "")

            # 確認恢復
            result = messagebox.askyesno(
                "確認恢復",
                f"確定要恢復以下配置嗎？\n\n"
                f"操作: {operation}\n\n"
                "這將覆蓋當前環境的伺服器配置。"
            )

            if result:
                success = self.ip_switcher.restore_from_history(history_index)
                if success:
                    messagebox.showinfo("成功", "配置恢復成功")
                    self.refresh_data()
                    self._reload_api_urls()
                else:
                    messagebox.showerror("錯誤", "配置恢復失敗")

        except Exception as e:
            logger.error(f"從歷史記錄恢復失敗: {e}")
            messagebox.showerror("錯誤", f"從歷史記錄恢復失敗: {e}")

    def clear_history(self):
        """清除歷史"""
        try:
            # 確認清除
            result = messagebox.askyesno(
                "確認清除",
                "確定要清除所有歷史記錄嗎？\n\n"
                "此操作無法復原！"
            )

            if result:
                success = self.ip_switcher.clear_history()
                if success:
                    messagebox.showinfo("成功", "歷史記錄清除成功")
                    self.refresh_data()
                else:
                    messagebox.showerror("錯誤", "清除歷史記錄失敗")

        except Exception as e:
            logger.error(f"清除歷史失敗: {e}")
            messagebox.showerror("錯誤", f"清除歷史失敗: {e}")

    def on_template_select(self, event):
        """模板選擇事件"""
        try:
            template_name = self.view.get_selected_template()
            if template_name:
                template_data = self.ip_switcher.get_template(template_name)
                if template_data:
                    self.view.show_template_details(template_data)

        except Exception as e:
            logger.error(f"處理模板選擇事件失敗: {e}")

    def edit_url(self):
        """編輯 URL"""
        try:
            selected_service = self.view.get_selected_url_service()
            if not selected_service:
                messagebox.showwarning("警告", "請先選擇要編輯的服務")
                return

            # 取得當前 URL
            current_env = self.env_config.get_current_environment()
            env_info = self.env_config.get_environment_info(current_env)
            current_url = env_info.get("api_servers", {}).get(selected_service, "")

            # 彈出編輯對話框
            from tkinter import simpledialog
            new_url = simpledialog.askstring(
                "編輯 URL",
                f"請輸入 {selected_service} 的新 URL:",
                initialvalue=current_url
            )

            if new_url and new_url != current_url:
                # 更新環境配置
                env_info["api_servers"][selected_service] = new_url
                self.env_config.update_environment(current_env, env_info)

                # 記錄歷史
                self.ip_switcher.add_history(
                    f"編輯 {selected_service} URL",
                    {selected_service: new_url}
                )

                # 更新顯示
                self.refresh_data()

                messagebox.showinfo("成功", f"已更新 {selected_service} 的 URL")
                logger.info(f"成功編輯 {selected_service} URL: {new_url}")

        except Exception as e:
            logger.error(f"編輯 URL 失敗: {e}")
            messagebox.showerror("錯誤", f"編輯 URL 失敗: {e}")

    def save_urls(self):
        """保存 URL 配置"""
        try:
            # 取得當前所有 URL
            urls = self.view.get_all_urls()
            if not urls:
                messagebox.showwarning("警告", "沒有 URL 配置可保存")
                return

            # 確認保存
            result = messagebox.askyesno(
                "確認保存",
                f"確定要保存當前的 {len(urls)} 個 URL 配置嗎？\n\n這將更新當前環境的配置。"
            )

            if result:
                # 更新環境配置
                current_env = self.env_config.get_current_environment()
                env_info = self.env_config.get_environment_info(current_env)
                env_info["api_servers"] = urls
                self.env_config.update_environment(current_env, env_info)

                # 記錄歷史
                self.ip_switcher.add_history("批量保存 URL 配置", urls)

                # 更新顯示
                self.refresh_data()

                messagebox.showinfo("成功", f"已保存 {len(urls)} 個 URL 配置")
                logger.info(f"成功保存 URL 配置: {len(urls)} 個服務")

        except Exception as e:
            logger.error(f"保存 URL 配置失敗: {e}")
            messagebox.showerror("錯誤", f"保存 URL 配置失敗: {e}")

    def reset_urls(self):
        """重置 URL 配置"""
        try:
            # 確認重置
            result = messagebox.askyesno(
                "確認重置",
                "確定要重置 URL 配置嗎？\n\n這將恢復到環境的原始配置。"
            )

            if result:
                # 重新載入環境配置
                current_env = self.env_config.get_current_environment()
                env_info = self.env_config.get_environment_info(current_env)
                api_servers = env_info.get("api_servers", {})

                # 更新 URL 列表顯示
                self.view.update_url_list(api_servers)

                messagebox.showinfo("成功", "已重置 URL 配置")
                logger.info("成功重置 URL 配置")

        except Exception as e:
            logger.error(f"重置 URL 配置失敗: {e}")
            messagebox.showerror("錯誤", f"重置 URL 配置失敗: {e}")

    def reload_urls(self):
        """重新載入 URL 配置"""
        try:
            # 重新載入環境配置
            self.env_config.reload_config()

            # 更新顯示
            self.refresh_data()

            messagebox.showinfo("成功", "已重新載入 URL 配置")
            logger.info("成功重新載入 URL 配置")

        except Exception as e:
            logger.error(f"重新載入 URL 配置失敗: {e}")
            messagebox.showerror("錯誤", f"重新載入 URL 配置失敗: {e}")

    def _reload_api_urls(self):
        """重新載入 API URLs"""
        try:
            # 重新載入 constants 模組中的 API_URLS
            from utils import constants
            constants.API_URLS = constants.get_api_urls()
            logger.info("API URLs 已重新載入")
        except Exception as e:
            logger.error(f"重新載入 API URLs 失敗: {e}")


class TemplateDialog:
    """模板配置對話框"""

    def __init__(self, parent, title: str, template_name: str = "", template_data: Dict = None):
        self.parent = parent
        self.title = title
        self.template_name = template_name
        self.template_data = template_data or {}
        self.result = None

        self.dialog = None
        self.name_var = tk.StringVar(value=template_name)
        self.description_var = tk.StringVar(value=template_data.get("description", ""))

        # 伺服器配置變數
        self.mysql_var = tk.StringVar(value=template_data.get("mysql_operator", ""))
        self.gamebridge_var = tk.StringVar(value=template_data.get("gamebridge", ""))
        self.tokenguard_var = tk.StringVar(value=template_data.get("tokenguard", ""))
        self.lottery_var = tk.StringVar(value=template_data.get("lottery", ""))
        self.simulation_var = tk.StringVar(value=template_data.get("simulation", ""))

    def show(self) -> Dict:
        """顯示對話框並返回結果"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("600x400")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        self.create_widgets()

        # 居中顯示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (self.dialog.winfo_width() // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (self.dialog.winfo_height() // 2)
        self.dialog.geometry(f"+{x}+{y}")

        # 等待對話框關閉
        self.dialog.wait_window()

        return self.result

    def create_widgets(self):
        """創建對話框元件"""
        # 基本資訊
        basic_frame = ttk.LabelFrame(self.dialog, text="基本資訊", padding=10)
        basic_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(basic_frame, text="模板名稱:").grid(row=0, column=0, sticky=tk.W, pady=2)
        name_entry = ttk.Entry(basic_frame, textvariable=self.name_var, width=50)
        name_entry.grid(row=0, column=1, sticky=tk.W, pady=2)

        ttk.Label(basic_frame, text="描述:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(basic_frame, textvariable=self.description_var, width=50).grid(row=1, column=1, sticky=tk.W, pady=2)

        # 如果是編輯模式，禁用名稱輸入
        if self.template_name:
            name_entry.config(state="readonly")

        # 伺服器配置
        server_frame = ttk.LabelFrame(self.dialog, text="伺服器配置", padding=10)
        server_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        ttk.Label(server_frame, text="MySQL 操作:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.mysql_var, width=60).grid(row=0, column=1, sticky=tk.W, pady=2)

        ttk.Label(server_frame, text="遊戲橋接:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.gamebridge_var, width=60).grid(row=1, column=1, sticky=tk.W, pady=2)

        ttk.Label(server_frame, text="令牌守護:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.tokenguard_var, width=60).grid(row=2, column=1, sticky=tk.W, pady=2)

        ttk.Label(server_frame, text="樂透服務:").grid(row=3, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.lottery_var, width=60).grid(row=3, column=1, sticky=tk.W, pady=2)

        ttk.Label(server_frame, text="模擬服務:").grid(row=4, column=0, sticky=tk.W, pady=2)
        ttk.Entry(server_frame, textvariable=self.simulation_var, width=60).grid(row=4, column=1, sticky=tk.W, pady=2)

        # 快速填入按鈕
        quick_frame = ttk.Frame(server_frame)
        quick_frame.grid(row=5, column=0, columnspan=2, pady=10)

        ttk.Button(quick_frame, text="使用當前配置", command=self.use_current_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(quick_frame, text="清空所有", command=self.clear_all).pack(side=tk.LEFT, padx=5)

        # 按鈕
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(button_frame, text="確定", command=self.ok_clicked).pack(side=tk.RIGHT, padx=5)
        ttk.Button(button_frame, text="取消", command=self.cancel_clicked).pack(side=tk.RIGHT)

    def use_current_config(self):
        """使用當前環境配置"""
        try:
            current_env = env_config.get_current_environment()
            env_info = env_config.get_environment_info(current_env)
            api_servers = env_info.get("api_servers", {})

            self.mysql_var.set(api_servers.get("mysql_operator", ""))
            self.gamebridge_var.set(api_servers.get("gamebridge", ""))
            self.tokenguard_var.set(api_servers.get("tokenguard", ""))
            self.lottery_var.set(api_servers.get("lottery", ""))
            self.simulation_var.set(api_servers.get("simulation", ""))

        except Exception as e:
            logger.error(f"使用當前配置失敗: {e}")
            messagebox.showerror("錯誤", f"使用當前配置失敗: {e}")

    def clear_all(self):
        """清空所有配置"""
        self.mysql_var.set("")
        self.gamebridge_var.set("")
        self.tokenguard_var.set("")
        self.lottery_var.set("")
        self.simulation_var.set("")

    def ok_clicked(self):
        """確定按鈕點擊事件"""
        # 驗證輸入
        if not self.name_var.get().strip():
            messagebox.showerror("錯誤", "請輸入模板名稱")
            return

        # 收集結果
        self.result = {
            "name": self.name_var.get().strip(),
            "description": self.description_var.get().strip(),
            "server_config": {
                "mysql_operator": self.mysql_var.get().strip(),
                "gamebridge": self.gamebridge_var.get().strip(),
                "tokenguard": self.tokenguard_var.get().strip(),
                "lottery": self.lottery_var.get().strip(),
                "simulation": self.simulation_var.get().strip()
            }
        }

        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按鈕點擊事件"""
        self.result = None
        self.dialog.destroy()
