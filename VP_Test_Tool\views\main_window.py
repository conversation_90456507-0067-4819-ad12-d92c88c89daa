"""主視窗介面"""
import tkinter as tk
from tkinter import ttk
import logging

# 初始化日誌記錄器
logger = logging.getLogger(__name__)
from utils.constants import WINDOW_WIDTH, WINDOW_HEIGHT, PADDING, APP_TITLE
from utils.config import Config
from utils.theme import ThemeManager
from utils.icon_manager import IconManager
from utils.ui_styles import TOOLBAR_STYLE, TAB_STYLE
from views.member_panel import MemberPanel
from views.rng_panel import RNGPanel
from views.account_panel import AccountPanel
from views.resource_panel import ResourcePanel
try:
    from views.feature_page import FeaturePage
    from utils.feature_detector import feature_detector
    FEATURE_PAGE_AVAILABLE = True
except ImportError:
    FEATURE_PAGE_AVAILABLE = False
try:
    from views.environment_panel import EnvironmentPanel
    from views.ip_switcher_panel import IPSwitcherPanel
    ENVIRONMENT_MANAGEMENT_AVAILABLE = True
except ImportError:
    ENVIRONMENT_MANAGEMENT_AVAILABLE = False
from widgets.modern_button import ModernButton
from widgets.notification import NotificationManager

class MainWindow:
    """主視窗類"""
    def __init__(self, root: tk.Tk, config: Config):
        self.root = root
        self.config = config
        # 初始化主題管理器
        self.theme_manager = ThemeManager()
        # 初始化標記，用於追蹤是否是第一次加載
        self._is_first_tab_change = True

        # 設定視窗屬性
        self.root.title(APP_TITLE)
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.minsize(800, 600)  # 設定最小視窗大小

        # 設定視窗圖示
        try:
            import os
            icon_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "assets", "icons", "vp_test_tool.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"無法載入應用程式圖示: {e}")

        # 初始化通知管理器
        self.notification_manager = NotificationManager(self.root)

        # 初始化介面元件
        self._init_ui()

    def _init_ui(self):
        """初始化 UI 元件"""
        # 工具列 - 使用統一樣式
        toolbar = ttk.Frame(
            self.root,
            padding=TOOLBAR_STYLE["padding"],
            style="Toolbar.TFrame"
        )
        toolbar.pack(fill="x", padx=PADDING, pady=5)

        # 麵包屑導航
        self.breadcrumb_frame = ttk.Frame(self.root)
        self.breadcrumb_frame.pack(fill="x", padx=PADDING, pady=(0, 5))

        # 麵包屑圖示
        self.home_icon = ttk.Label(
            self.breadcrumb_frame,
            text=IconManager.get('home'),
            font=("TkDefaultFont", 12),
            foreground=self.theme_manager.get_color("primary"),
            cursor="hand2"  # 添加手型游標，提示可點擊
        )
        self.home_icon.pack(side="left")

        # 綁定點擊事件，返回首頁
        self.home_icon.bind("<Button-1>", lambda e: self.notebook.select(0))

        # 麵包屑分隔符
        self.separator_label = ttk.Label(
            self.breadcrumb_frame,
            text=" > ",
            font=self.theme_manager.get_font("small"),
            foreground=self.theme_manager.get_color("text_secondary")
        )
        self.separator_label.pack(side="left")

        # 麵包屑標籤
        self.breadcrumb_label = ttk.Label(
            self.breadcrumb_frame,
            text="資源調整工具",  # 預設頁面
            font=self.theme_manager.get_font("small"),
            foreground=self.theme_manager.get_color("text_primary")
        )
        self.breadcrumb_label.pack(side="left")

        # 添加應用程式標題
        title_label = ttk.Label(
            toolbar,
            text=APP_TITLE,
            font=("Microsoft JhengHei UI", 16, "bold"),
            foreground="#0D47A1"  # 深藍色
        )
        title_label.pack(side="left", padx=PADDING)

        # 使用現代化按鈕
        self.settings_button = ModernButton(
            toolbar,
            text="設定",
            icon=IconManager.get('settings'),
            command=self._open_settings,
            button_type="info"
        )
        self.settings_button.pack(side="right", padx=PADDING)

        # 建立主要的 Notebook
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(expand=True, fill='both', padx=PADDING, pady=5)

        # 設定其他 Notebook 樣式
        self._setup_notebook_style()

        # 綁定頁籤切換事件
        self.notebook.bind("<<NotebookTabChanged>>", self._on_tab_changed)

        # 資源調整工具頁面 - 傳遞主視窗引用以顯示通知
        self.resource_panel = ResourcePanel(self.notebook, self)
        self.notebook.add(
            self.resource_panel,
            text=f"  {IconManager.get('money')}  資源調整工具  "  # 增加空格使文字更易讀
        )

        # 遊戲卡片工具頁面
        self.member_panel = MemberPanel(self.notebook)
        self.notebook.add(
            self.member_panel,
            text=f"  {IconManager.get('game')}  遊戲卡片工具  "  # 增加空格使文字更易讀
        )

        # 帳號產生器頁面
        self.account_panel = AccountPanel(self.notebook)
        self.notebook.add(
            self.account_panel,
            text=f"  {IconManager.get('user')}  帳號產生器  "  # 增加空格使文字更易讀
        )

        # Slot Set RNG 頁面
        self.rng_panel = RNGPanel(self.notebook)
        self.notebook.add(
            self.rng_panel,
            text=f"  {IconManager.get('dice')}  Slot Set RNG  "  # 增加空格使文字更易讀
        )

        # 環境管理頁面
        if ENVIRONMENT_MANAGEMENT_AVAILABLE:
            try:
                # IP 切換工具頁面 - 移到環境管理前面並更名
                self.ip_switcher_panel = IPSwitcherPanel(self.notebook)
                self.notebook.add(
                    self.ip_switcher_panel,
                    text="  API IP 切換工具  "  # 移除 network 圖示
                )
                logger.info("成功添加 API IP 切換工具頁面")
            except Exception as e:
                logger.warning(f"添加 API IP 切換工具頁面失敗: {e}")

            try:
                self.environment_panel = EnvironmentPanel(self.notebook)
                self.notebook.add(
                    self.environment_panel,
                    text=f"  {IconManager.get('settings')}  環境管理  "
                )
                logger.info("成功添加環境管理頁面")
            except Exception as e:
                logger.warning(f"添加環境管理頁面失敗: {e}")

        # 功能檢測頁面
        if FEATURE_PAGE_AVAILABLE:
            try:
                self.feature_page = FeaturePage(self.notebook, feature_detector)
                self.notebook.add(
                    self.feature_page,
                    text=f"  {IconManager.get('tools')}  功能檢測  "  # 增加空格使文字更易讀
                )
                logger.info("成功添加功能檢測頁面")
            except Exception as e:
                logger.warning(f"添加功能檢測頁面失敗: {e}")

        # 設定預設頁籤為資源調整工具（第一個頁籤）
        self.notebook.select(0)

        # 導航提示區域
        self.navigation_frame = ttk.Frame(self.root, padding=PADDING)
        self.navigation_frame.pack(fill=tk.X, padx=PADDING, pady=(0, PADDING))

        # 導航提示標題
        self.navigation_title = ttk.Label(
            self.navigation_frame,
            text="快速導航",
            font=self.theme_manager.get_font("subtitle"),
            foreground=self.theme_manager.get_color("primary")
        )
        self.navigation_title.pack(anchor="w", pady=(0, 5))

        # 導航提示內容
        self.navigation_tips = [
            ("資源調整工具 (Ctrl+1)", "用於調整會員資源，包括金幣、VIP、寶石和抽獎券"),
            ("遊戲卡片工具 (Ctrl+2)", "用於管理會員的遊戲卡片，包括新增、刪除和修改"),
            ("帳號產生器 (Ctrl+3)", "用於批量產生會員帳號，支援不同代理商和幣別"),
            ("Slot Set RNG (Ctrl+4)", "用於設定老虎機的隨機數生成器參數")
        ]

        # 如果環境管理功能可用，添加到導航提示
        if ENVIRONMENT_MANAGEMENT_AVAILABLE:
            self.navigation_tips.extend([
                ("API IP 切換工具 (Ctrl+5)", "用於快速切換 API IP 地址，支援模板管理和歷史記錄"),
                ("環境管理 (Ctrl+6)", "用於管理多個環境配置，支援環境切換、備份和恢復")
            ])

        # 如果功能檢測頁面可用，添加到導航提示
        if FEATURE_PAGE_AVAILABLE:
            ctrl_key = "Ctrl+7" if ENVIRONMENT_MANAGEMENT_AVAILABLE else "Ctrl+5"
            self.navigation_tips.append(
                (f"功能檢測 ({ctrl_key})", "用於檢測系統中可用的功能，包括內存監控、網絡恢復等")
            )

        # 創建導航提示按鈕
        for i, (title, desc) in enumerate(self.navigation_tips):
            tip_frame = ttk.Frame(self.navigation_frame)
            tip_frame.pack(fill=tk.X, pady=2)

            # 導航按鈕
            btn = ModernButton(
                tip_frame,
                text=title,
                command=lambda idx=i: self.notebook.select(idx),
                button_type="info",
                width=20
            )
            btn.pack(side=tk.LEFT)

            # 描述標籤
            desc_label = ttk.Label(
                tip_frame,
                text=desc,
                font=self.theme_manager.get_font("small"),
                foreground=self.theme_manager.get_color("text_secondary")
            )
            desc_label.pack(side=tk.LEFT, padx=(10, 0), fill=tk.X, expand=True)

        # 狀態列
        self.status_bar = ttk.Label(
            self.root,
            text="就緒",
            relief=tk.SUNKEN,
            font=self.theme_manager.get_font("small"),
            padding=(PADDING, 2)
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def show_message(self, message: str, is_error: bool = False):
        """顯示狀態消息"""
        self.status_bar.config(
            text=message,
            foreground=self.theme_manager.get_color("danger") if is_error else self.theme_manager.get_color("text_primary")
        )

    def set_title(self, title: str):
        """設置視窗標題"""
        self.root.title(f"{APP_TITLE} - {title}")

    def _setup_notebook_style(self):
        """設定 Notebook 樣式"""
        style = ttk.Style()

        # 檢查主題是否已經存在
        theme_exists = False
        try:
            # 獲取所有可用主題
            available_themes = style.theme_names()
            theme_exists = "MyTheme" in available_themes
        except Exception as e:
            print(f"檢查主題時發生異常: {e}")

        # 如果主題不存在，則創建
        if not theme_exists:
            try:
                # 定義自定義樣式
                style.theme_create("MyTheme", parent="alt", settings={
                    "TNotebook": {
                        "configure": {
                            "tabmargins": [2, 5, 2, 0],
                            "background": "#E3F2FD",  # 淺藍色背景
                            "borderwidth": 0
                        }
                    },
                    "TNotebook.Tab": {
                        "configure": {
                            "padding": [20, 10],
                            "font": ("Microsoft JhengHei UI", 12, "bold"),
                            "background": "#FFFFFF",  # 白色背景
                            "foreground": "#000000",  # 黑色文字
                            "borderwidth": 1,
                            "relief": "solid"
                        },
                        "map": {
                            "background": [("selected", "#1E88E5")],  # 選中時的背景色為亮藍色
                            "foreground": [("selected", "#FFFFFF")],  # 選中時的文字顏色為白色
                            "expand": [("selected", [1, 1, 1, 0])]
                        }
                    }
                })
            except Exception as e:
                print(f"創建主題時發生異常: {e}")

        # 使用自定義樣式
        try:
            style.theme_use("MyTheme")
        except Exception as e:
            print(f"使用主題時發生異常: {e}")
            # 如果無法使用自定義主題，則使用默認主題
            try:
                style.theme_use("default")
            except Exception:
                pass

    def _open_settings(self):
        """開啟設定視窗"""
        from views.settings_window import SettingsWindow
        SettingsWindow(self.root, self.config)

    def show_notification(self, message: str, notification_type: str = "info", duration: int = 3000):
        """顯示通知

        Args:
            message: 通知訊息
            notification_type: 通知類型，可以是 'info', 'success', 'warning', 'error'
            duration: 顯示時間 (毫秒)
        """
        # 使用通知管理器顯示通知
        if notification_type == "success":
            self.notification_manager.show_success(message, duration)
        elif notification_type == "warning":
            self.notification_manager.show_warning(message, duration)
        elif notification_type == "error":
            self.notification_manager.show_error(message, duration)
        else:  # info
            self.notification_manager.show_info(message, duration)

    def _on_tab_changed(self, _):
        """頁籤切換時更新麵包屑"""
        tab_id = self.notebook.select()
        tab_text = self.notebook.tab(tab_id, "text").strip()

        # 移除圖示和多餘空格
        for icon in IconManager.get_all().values():
            tab_text = tab_text.replace(icon, "").strip()

        # 更新麵包屑標籤
        self.breadcrumb_label.config(text=tab_text)



        # 只在非第一次加載時顯示通知
        if not self._is_first_tab_change:
            # 顯示相關頁面的小提示
            self.show_notification(
                f"已切換到 {tab_text} 頁面",
                notification_type="info",
                duration=1500
            )
        else:
            # 第一次加載後將標記設為 False
            self._is_first_tab_change = False


