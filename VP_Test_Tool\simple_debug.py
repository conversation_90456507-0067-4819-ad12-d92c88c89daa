#!/usr/bin/env python3
"""
簡單除錯腳本
逐步測試各個模組的導入和初始化
"""

import sys
import os
import traceback

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_step(step_name, func):
    """測試步驟"""
    print(f"🔍 測試: {step_name}")
    try:
        result = func()
        print(f"✅ 成功: {step_name}")
        return result
    except Exception as e:
        print(f"❌ 失敗: {step_name} - {e}")
        traceback.print_exc()
        return None

def test_basic_imports():
    """測試基本導入"""
    print("=== 測試基本導入 ===")
    
    def import_tkinter():
        import tkinter as tk
        from tkinter import ttk
        return True
    
    def import_logger():
        from utils.enhanced_logger import enhanced_logger
        return enhanced_logger
    
    def import_config():
        from utils.config import Config
        return Config()
    
    test_step("導入 tkinter", import_tkinter)
    test_step("導入 enhanced_logger", import_logger)
    test_step("導入 Config", import_config)

def test_ip_switcher_imports():
    """測試 IP 切換工具相關導入"""
    print("\n=== 測試 IP 切換工具導入 ===")
    
    def import_env_config():
        from utils.environment_config import env_config
        return env_config
    
    def import_ip_switcher():
        from utils.ip_switcher import ip_switcher
        return ip_switcher
    
    def import_panel():
        from views.ip_switcher_panel import IPSwitcherPanel
        return IPSwitcherPanel
    
    def import_controller():
        from controllers.ip_switcher_controller import IPSwitcherController
        return IPSwitcherController
    
    test_step("導入 environment_config", import_env_config)
    test_step("導入 ip_switcher", import_ip_switcher)
    test_step("導入 IPSwitcherPanel", import_panel)
    test_step("導入 IPSwitcherController", import_controller)

def test_simple_ui():
    """測試簡單 UI 創建"""
    print("\n=== 測試簡單 UI 創建 ===")
    
    def create_root():
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隱藏視窗
        return root
    
    def create_panel():
        import tkinter as tk
        from views.ip_switcher_panel import IPSwitcherPanel
        
        root = tk.Tk()
        root.withdraw()
        panel = IPSwitcherPanel(root)
        root.destroy()
        return True
    
    def create_controller():
        import tkinter as tk
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        
        root = tk.Tk()
        root.withdraw()
        panel = IPSwitcherPanel(root)
        controller = IPSwitcherController(panel)
        root.destroy()
        return True
    
    test_step("創建根視窗", create_root)
    test_step("創建 IP 切換面板", create_panel)
    test_step("創建 IP 切換控制器", create_controller)

def test_environment_operations():
    """測試環境配置操作"""
    print("\n=== 測試環境配置操作 ===")
    
    def get_current_env():
        from utils.environment_config import env_config
        return env_config.get_current_environment()
    
    def get_env_info():
        from utils.environment_config import env_config
        current_env = env_config.get_current_environment()
        return env_config.get_environment_info(current_env)
    
    def check_api_servers():
        from utils.environment_config import env_config
        current_env = env_config.get_current_environment()
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        print(f"   找到 {len(api_servers)} 個 API 伺服器")
        return len(api_servers) > 0
    
    test_step("取得當前環境", get_current_env)
    test_step("取得環境資訊", get_env_info)
    test_step("檢查 API 伺服器", check_api_servers)

def test_main_window_components():
    """測試主視窗元件"""
    print("\n=== 測試主視窗元件 ===")
    
    def import_main_window():
        from views.main_window import MainWindow
        return MainWindow
    
    def create_main_window():
        import tkinter as tk
        from utils.config import Config
        from views.main_window import MainWindow
        
        root = tk.Tk()
        root.withdraw()
        config = Config()
        
        # 這裡可能會卡住
        print("   正在創建主視窗...")
        main_window = MainWindow(root, config)
        print("   主視窗創建完成")
        
        root.destroy()
        return True
    
    test_step("導入 MainWindow", import_main_window)
    test_step("創建主視窗", create_main_window)

def main():
    """主函數"""
    print("簡單除錯腳本開始")
    print("=" * 50)
    
    # 按順序執行測試
    test_basic_imports()
    test_ip_switcher_imports()
    test_environment_operations()
    test_simple_ui()
    test_main_window_components()
    
    print("\n" + "=" * 50)
    print("除錯完成")

if __name__ == "__main__":
    main()
