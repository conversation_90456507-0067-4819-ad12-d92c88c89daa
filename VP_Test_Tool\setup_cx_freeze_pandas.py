"""
使用 cx_Freeze 打包 VP Test Tool 為 exe 文件
包含 pandas 和相關依賴的版本
"""
import sys
import os
from cx_Freeze import setup, Executable
import pandas
import numpy
import openpyxl
import dateutil
import gzip
import ssl
import re
import et_xmlfile

# 應用程式信息
APP_NAME = "VP_Test_Tool"
APP_VERSION = "2.6.2"
APP_DESCRIPTION = "VP Test Tool"
APP_AUTHOR = "VP Test Tool Team"

# 圖示路徑
icon_path = os.path.abspath(os.path.join("assets", "icons", "vp_test_tool.ico"))
if not os.path.exists(icon_path):
    print(f"Warning: Icon file not found at {icon_path}")
    icon_path = None

# 基本設置
base = None
if sys.platform == "win32":
    base = "Win32GUI"  # 使用 GUI 模式，不顯示控制台窗口

# 包含的文件和目錄
include_files = [
    ("assets", "assets"),  # 包含資源文件
    ("config.json", "config.json"),  # 包含配置文件
]

# 獲取 pandas, numpy, openpyxl, dateutil 和 et_xmlfile 的路徑
pandas_path = os.path.dirname(pandas.__file__)
numpy_path = os.path.dirname(numpy.__file__)
openpyxl_path = os.path.dirname(openpyxl.__file__)
dateutil_path = os.path.dirname(dateutil.__file__)
et_xmlfile_path = os.path.dirname(et_xmlfile.__file__)

# 添加 pandas, numpy, openpyxl, dateutil 和 et_xmlfile 到包含文件中
include_files.append((pandas_path, "lib/pandas"))
include_files.append((numpy_path, "lib/numpy"))
include_files.append((openpyxl_path, "lib/openpyxl"))
include_files.append((dateutil_path, "lib/dateutil"))
include_files.append((et_xmlfile_path, "lib/et_xmlfile"))

# 構建選項
build_options = {
    "include_files": include_files,
    "build_exe": "dist/pandas_build",  # 輸出目錄
    "optimize": 0,  # 不進行優化，保留所有模組
    "include_msvcr": True,  # 包含 MSVC 運行時
    "silent": False,  # 顯示詳細輸出
    "packages": ["tkinter", "PIL", "utils", "views", "models", "controllers", "widgets",
                "json", "logging", "requests", "urllib3", "idna", "certifi", "chardet",
                "charset_normalizer", "http", "email", "xml", "html", "pandas", "numpy",
                "pytz", "dateutil", "six", "numbers", "decimal", "openpyxl", "et_xmlfile",
                "io", "tempfile", "pathlib", "subprocess", "glob", "re", "gzip",
                "ssl", "urllib", "urllib.request", "urllib.parse", "threading", "base64"],
    "excludes": ["unittest", "pydoc", "doctest", "scipy", "matplotlib",
                "PyQt5", "PyQt6", "PySide2", "PySide6", "IPython", "jupyter", "notebook"],
    "zip_include_packages": "*",  # 包含所有包
    "zip_exclude_packages": "",  # 不排除任何包
}

# 可執行文件選項
executables = [
    Executable(
        "main.py",  # 主程序文件
        base=base,
        target_name=APP_NAME,  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=APP_NAME,  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加診斷模式可執行文件
    Executable(
        "debug_startup.py",  # 診斷啟動腳本
        base=base,
        target_name=f"{APP_NAME}_Diagnostic",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Diagnostic",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加網絡測試模式可執行文件
    Executable(
        "network_test.py",  # 網絡測試腳本
        base=base,
        target_name=f"{APP_NAME}_NetworkTest",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Network Test",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    ),
    # 添加簡單模式可執行文件
    Executable(
        "simple_startup.py",  # 簡單啟動腳本
        base=base,
        target_name=f"{APP_NAME}_Simple",  # 輸出文件名
        icon=icon_path,  # 圖示
        copyright=f"Copyright (c) {APP_AUTHOR}",  # 版權信息
        shortcut_name=f"{APP_NAME} Simple",  # 快捷方式名稱
        shortcut_dir="DesktopFolder",  # 在桌面創建快捷方式
    )
]

# 設置
setup(
    name=APP_NAME,
    version=APP_VERSION,
    description=APP_DESCRIPTION,
    author=APP_AUTHOR,
    options={"build_exe": build_options},
    executables=executables
)

print(f"打包完成！exe 文件位於: {os.path.abspath(os.path.join('dist', 'pandas_build', APP_NAME + '.exe'))}")
print(f"診斷模式 exe 文件位於: {os.path.abspath(os.path.join('dist', 'pandas_build', APP_NAME + '_Diagnostic.exe'))}")
print(f"網絡測試模式 exe 文件位於: {os.path.abspath(os.path.join('dist', 'pandas_build', APP_NAME + '_NetworkTest.exe'))}")
print(f"簡單模式 exe 文件位於: {os.path.abspath(os.path.join('dist', 'pandas_build', APP_NAME + '_Simple.exe'))}")
