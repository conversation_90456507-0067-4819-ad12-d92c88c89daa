#!/usr/bin/env python3
"""
測試導入問題
逐步測試每個導入，找出導致卡住的模組
"""

import sys
import os

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import_step_by_step():
    """逐步測試導入"""
    print("開始逐步測試導入...")
    
    imports = [
        ("tkinter", "import tkinter as tk"),
        ("os", "import os"),
        ("threading", "import threading"),
        ("gc", "import gc"),
        ("version", "from utils.version import APP_TITLE"),
        ("config", "from utils.config import Config"),
        ("enhanced_logger", "from utils.enhanced_logger import enhanced_logger"),
        ("http_client_enhanced", "from utils.http_client_enhanced import HttpClientEnhanced"),
        ("memory_monitor", "from utils.memory_monitor import memory_monitor"),
        ("network_recovery", "from utils.network_recovery import network_recovery"),
        ("feature_detector", "from utils.feature_detector import feature_detector"),
        ("theme", "from utils.theme import ThemeManager"),
        ("keyboard_shortcuts", "from utils.keyboard_shortcuts import KeyboardShortcuts"),
        ("settings_window", "from views.settings_window import SettingsWindow"),
        ("resource_monitor", "from widgets.resource_monitor import ResourceMonitor"),
        ("member", "from models.member import MemberService"),
        ("agent", "from models.agent import AgentService"),
        ("member_controller", "from controllers.member_controller import MemberController"),
        ("resource_controller", "from controllers.resource_controller import ResourceController"),
        ("account_controller", "from controllers.account_controller import AccountController"),
        ("rng_controller", "from controllers.rng_controller import RNGController"),
        ("main_window", "from views.main_window import MainWindow"),  # 這個可能有問題
    ]
    
    for name, import_stmt in imports:
        print(f"🔍 測試導入: {name}")
        try:
            exec(import_stmt)
            print(f"✅ 成功: {name}")
        except Exception as e:
            print(f"❌ 失敗: {name} - {e}")
            import traceback
            traceback.print_exc()
            print(f"⚠️ 停止測試，因為 {name} 導入失敗")
            return False
        print()
    
    print("✅ 所有導入測試通過")
    return True

def test_main_window_import():
    """專門測試 MainWindow 導入"""
    print("=== 專門測試 MainWindow 導入 ===")
    
    try:
        print("🔍 測試導入 MainWindow...")
        from views.main_window import MainWindow
        print("✅ MainWindow 導入成功")
        
        print("🔍 測試創建 MainWindow...")
        import tkinter as tk
        from utils.config import Config
        
        root = tk.Tk()
        root.withdraw()
        config = Config()
        
        print("   正在創建 MainWindow 實例...")
        main_window = MainWindow(root, config)
        print("✅ MainWindow 創建成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ MainWindow 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("導入測試開始")
    print("=" * 50)
    
    # 先測試逐步導入
    if test_import_step_by_step():
        print("\n" + "=" * 50)
        # 如果基本導入成功，專門測試 MainWindow
        test_main_window_import()
    
    print("\n" + "=" * 50)
    print("導入測試完成")

if __name__ == "__main__":
    main()
