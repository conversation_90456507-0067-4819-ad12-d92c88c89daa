"""快速 IP 切換界面"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import logging
from typing import Dict, List
from utils.constants import PADDING, FONT_TITLE, FONT_LABEL, FONT_TEXT

logger = logging.getLogger(__name__)

class IPSwitcherPanel(ttk.Frame):
    """快速 IP 切換界面"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent

        self.create_widgets()
        self.setup_layout()

    def create_widgets(self):
        """創建界面元件"""
        # 標題
        self.title_label = ttk.Label(
            self,
            text="快速 IP 切換工具",
            font=FONT_TITLE
        )

        # 快速切換區域
        self.quick_switch_frame = ttk.LabelFrame(self, text="快速 IP 切換", padding=PADDING)

        # 當前 IP 顯示
        self.current_ip_frame = ttk.Frame(self.quick_switch_frame)
        ttk.Label(self.current_ip_frame, text="當前常用 IP:", font=FONT_LABEL).pack(side=tk.LEFT)
        self.current_ip_var = tk.StringVar()
        self.current_ip_label = ttk.Label(
            self.current_ip_frame,
            textvariable=self.current_ip_var,
            font=FONT_TEXT,
            foreground="blue"
        )
        self.current_ip_label.pack(side=tk.LEFT, padx=(10, 0))

        # IP 切換輸入
        self.switch_frame = ttk.Frame(self.quick_switch_frame)

        ttk.Label(self.switch_frame, text="舊 IP:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.old_ip_var = tk.StringVar()
        self.old_ip_entry = ttk.Entry(self.switch_frame, textvariable=self.old_ip_var, width=20)
        self.old_ip_entry.grid(row=0, column=1, padx=5, pady=2)

        ttk.Label(self.switch_frame, text="新 IP:").grid(row=0, column=2, sticky=tk.W, padx=(10, 0), pady=2)
        self.new_ip_var = tk.StringVar()
        self.new_ip_entry = ttk.Entry(self.switch_frame, textvariable=self.new_ip_var, width=20)
        self.new_ip_entry.grid(row=0, column=3, padx=5, pady=2)

        self.switch_btn = ttk.Button(
            self.switch_frame,
            text="快速切換",
            command=self.quick_switch_ip
        )
        self.switch_btn.grid(row=0, column=4, padx=10, pady=2)

        # IP 模板區域
        self.template_frame = ttk.LabelFrame(self, text="IP 模板", padding=PADDING)

        # 模板列表
        self.template_listbox = tk.Listbox(
            self.template_frame,
            height=6,
            font=FONT_TEXT
        )

        self.template_scrollbar = ttk.Scrollbar(
            self.template_frame,
            orient="vertical",
            command=self.template_listbox.yview
        )
        self.template_listbox.configure(yscrollcommand=self.template_scrollbar.set)

        # 模板操作按鈕
        self.template_buttons_frame = ttk.Frame(self.template_frame)

        self.apply_template_btn = ttk.Button(
            self.template_buttons_frame,
            text="應用模板",
            command=self.apply_template
        )

        self.add_template_btn = ttk.Button(
            self.template_buttons_frame,
            text="新增模板",
            command=self.add_template
        )

        self.edit_template_btn = ttk.Button(
            self.template_buttons_frame,
            text="編輯模板",
            command=self.edit_template
        )

        self.delete_template_btn = ttk.Button(
            self.template_buttons_frame,
            text="刪除模板",
            command=self.delete_template
        )

        # 歷史記錄區域
        self.history_frame = ttk.LabelFrame(self, text="切換歷史", padding=PADDING)

        # 歷史列表
        columns = ("time", "operation", "details")
        self.history_tree = ttk.Treeview(
            self.history_frame,
            columns=columns,
            show="headings",
            height=8
        )

        self.history_tree.heading("time", text="時間")
        self.history_tree.heading("operation", text="操作")
        self.history_tree.heading("details", text="詳情")

        self.history_tree.column("time", width=150)
        self.history_tree.column("operation", width=200)
        self.history_tree.column("details", width=300)

        self.history_scrollbar = ttk.Scrollbar(
            self.history_frame,
            orient="vertical",
            command=self.history_tree.yview
        )
        self.history_tree.configure(yscrollcommand=self.history_scrollbar.set)

        # 歷史操作按鈕
        self.history_buttons_frame = ttk.Frame(self.history_frame)

        self.restore_btn = ttk.Button(
            self.history_buttons_frame,
            text="恢復配置",
            command=self.restore_from_history
        )

        self.clear_history_btn = ttk.Button(
            self.history_buttons_frame,
            text="清除歷史",
            command=self.clear_history
        )

        self.refresh_btn = ttk.Button(
            self.history_buttons_frame,
            text="重新整理",
            command=self.refresh_data
        )

        # URL 編輯區域
        self.url_edit_frame = ttk.LabelFrame(self, text="URL 配置編輯", padding=PADDING)

        # URL 編輯表格
        self.url_tree_frame = ttk.Frame(self.url_edit_frame)

        # 創建 Treeview 來顯示和編輯 URL
        columns = ("service", "url")
        self.url_tree = ttk.Treeview(
            self.url_tree_frame,
            columns=columns,
            show="headings",
            height=6
        )

        self.url_tree.heading("service", text="服務名稱")
        self.url_tree.heading("url", text="URL 地址")

        self.url_tree.column("service", width=150)
        self.url_tree.column("url", width=400)

        self.url_tree_scrollbar = ttk.Scrollbar(
            self.url_tree_frame,
            orient="vertical",
            command=self.url_tree.yview
        )
        self.url_tree.configure(yscrollcommand=self.url_tree_scrollbar.set)

        # URL 編輯按鈕
        self.url_buttons_frame = ttk.Frame(self.url_edit_frame)

        self.edit_url_btn = ttk.Button(
            self.url_buttons_frame,
            text="編輯 URL",
            command=self.edit_url
        )

        self.save_urls_btn = ttk.Button(
            self.url_buttons_frame,
            text="保存配置",
            command=self.save_urls
        )

        self.reset_urls_btn = ttk.Button(
            self.url_buttons_frame,
            text="重置配置",
            command=self.reset_urls
        )

        self.reload_urls_btn = ttk.Button(
            self.url_buttons_frame,
            text="重新載入",
            command=self.reload_urls
        )

        # 當前配置顯示區域
        self.current_config_frame = ttk.LabelFrame(self, text="當前 IP 配置", padding=PADDING)

        self.config_text = tk.Text(
            self.current_config_frame,
            height=6,
            width=50,
            font=FONT_TEXT,
            wrap=tk.WORD,
            state=tk.DISABLED
        )

        self.config_scrollbar = ttk.Scrollbar(
            self.current_config_frame,
            orient="vertical",
            command=self.config_text.yview
        )
        self.config_text.configure(yscrollcommand=self.config_scrollbar.set)

        # 綁定事件
        self.template_listbox.bind("<Double-1>", lambda e: self.apply_template())
        self.history_tree.bind("<Double-1>", lambda e: self.restore_from_history())
        self.template_listbox.bind("<<ListboxSelect>>", self.on_template_select)
        self.url_tree.bind("<Double-1>", lambda e: self.edit_url())

    def setup_layout(self):
        """設置界面佈局"""
        # 標題
        self.title_label.pack(pady=(0, PADDING))

        # 主要內容區域
        main_frame = ttk.Frame(self)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 左側區域
        left_frame = ttk.Frame(main_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, PADDING//2))

        # 快速切換區域
        self.quick_switch_frame.pack(fill=tk.X, pady=(0, PADDING))
        self.current_ip_frame.pack(fill=tk.X, pady=(0, PADDING))
        self.switch_frame.pack(fill=tk.X)

        # IP 模板區域
        self.template_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))

        # 模板列表和滾動條
        template_list_frame = ttk.Frame(self.template_frame)
        template_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))

        self.template_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.template_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 模板操作按鈕
        self.template_buttons_frame.pack(fill=tk.X)
        self.apply_template_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.add_template_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.edit_template_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.delete_template_btn.pack(side=tk.LEFT)

        # 右側區域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(PADDING//2, 0))

        # URL 編輯區域
        self.url_edit_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))

        # URL 編輯表格和滾動條
        self.url_tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))
        self.url_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.url_tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # URL 編輯按鈕
        self.url_buttons_frame.pack(fill=tk.X)
        self.edit_url_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.save_urls_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.reset_urls_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.reload_urls_btn.pack(side=tk.LEFT)

        # 歷史記錄區域
        self.history_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))

        # 歷史樹狀圖和滾動條
        history_tree_frame = ttk.Frame(self.history_frame)
        history_tree_frame.pack(fill=tk.BOTH, expand=True, pady=(0, PADDING))

        self.history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 歷史操作按鈕
        self.history_buttons_frame.pack(fill=tk.X)
        self.restore_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.clear_history_btn.pack(side=tk.LEFT, padx=(0, 5))
        self.refresh_btn.pack(side=tk.LEFT)

        # 當前配置顯示區域
        self.current_config_frame.pack(fill=tk.BOTH, expand=True)

        config_content_frame = ttk.Frame(self.current_config_frame)
        config_content_frame.pack(fill=tk.BOTH, expand=True)

        self.config_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        self.config_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def quick_switch_ip(self):
        """快速切換 IP"""
        # 這裡會由控制器實現具體邏輯
        pass

    def apply_template(self):
        """應用模板"""
        # 這裡會由控制器實現具體邏輯
        pass

    def add_template(self):
        """新增模板"""
        # 這裡會由控制器實現具體邏輯
        pass

    def edit_template(self):
        """編輯模板"""
        # 這裡會由控制器實現具體邏輯
        pass

    def delete_template(self):
        """刪除模板"""
        # 這裡會由控制器實現具體邏輯
        pass

    def restore_from_history(self):
        """從歷史記錄恢復"""
        # 這裡會由控制器實現具體邏輯
        pass

    def clear_history(self):
        """清除歷史"""
        # 這裡會由控制器實現具體邏輯
        pass

    def refresh_data(self):
        """重新整理數據"""
        # 這裡會由控制器實現具體邏輯
        pass

    def on_template_select(self, event):
        """模板選擇事件"""
        # 這裡會由控制器實現具體邏輯
        pass

    def edit_url(self):
        """編輯 URL"""
        # 這裡會由控制器實現具體邏輯
        pass

    def save_urls(self):
        """保存 URL 配置"""
        # 這裡會由控制器實現具體邏輯
        pass

    def reset_urls(self):
        """重置 URL 配置"""
        # 這裡會由控制器實現具體邏輯
        pass

    def reload_urls(self):
        """重新載入 URL 配置"""
        # 這裡會由控制器實現具體邏輯
        pass

    def update_template_list(self, templates: Dict[str, Dict]):
        """更新模板列表"""
        self.template_listbox.delete(0, tk.END)
        for template_name, template_data in templates.items():
            description = template_data.get("description", "")
            display_text = f"{template_name} - {description}" if description else template_name
            self.template_listbox.insert(tk.END, display_text)

    def update_history_list(self, history: List[Dict]):
        """更新歷史列表"""
        # 清空現有項目
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # 添加新項目
        for entry in history:
            timestamp = entry.get("timestamp", "")
            operation = entry.get("operation", "")
            server_config = entry.get("server_config", {})

            # 格式化時間
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                time_str = dt.strftime("%m-%d %H:%M")
            except:
                time_str = timestamp[:16] if len(timestamp) > 16 else timestamp

            # 格式化詳情
            details = ", ".join([f"{k}: {v}" for k, v in list(server_config.items())[:2]])
            if len(server_config) > 2:
                details += f" ... (共{len(server_config)}項)"

            self.history_tree.insert("", tk.END, values=(time_str, operation, details))

    def update_current_config(self, config: Dict[str, str]):
        """更新當前配置顯示"""
        self.config_text.config(state=tk.NORMAL)
        self.config_text.delete(1.0, tk.END)

        for server_type, url in config.items():
            self.config_text.insert(tk.END, f"{server_type}:\n  {url}\n\n")

        self.config_text.config(state=tk.DISABLED)

    def set_current_ip(self, ip: str):
        """設置當前 IP 顯示"""
        self.current_ip_var.set(ip)
        # 自動填入舊 IP 欄位
        self.old_ip_var.set(ip)

    def get_selected_template(self) -> str:
        """取得選中的模板名稱"""
        selection = self.template_listbox.curselection()
        if selection:
            selected_text = self.template_listbox.get(selection[0])
            # 提取模板名稱（去除描述部分）
            template_name = selected_text.split(" - ")[0]
            return template_name
        return ""

    def get_selected_history_index(self) -> int:
        """取得選中的歷史記錄索引"""
        selection = self.history_tree.selection()
        if selection:
            # 取得選中項目在樹狀圖中的索引
            item = selection[0]
            children = self.history_tree.get_children()
            return children.index(item)
        return -1

    def show_template_details(self, template_data: Dict[str, str]):
        """顯示模板詳情"""
        details = []
        for key, value in template_data.items():
            if key != "description":
                details.append(f"{key}: {value}")

        detail_text = "\n".join(details)
        messagebox.showinfo("模板詳情", detail_text)

    def update_url_list(self, urls: Dict[str, str]):
        """更新 URL 列表"""
        # 清空現有項目
        for item in self.url_tree.get_children():
            self.url_tree.delete(item)

        # 添加新項目
        for service_name, url in urls.items():
            self.url_tree.insert("", tk.END, values=(service_name, url))

    def get_selected_url_service(self) -> str:
        """取得選中的 URL 服務名稱"""
        selection = self.url_tree.selection()
        if selection:
            item = selection[0]
            values = self.url_tree.item(item, "values")
            return values[0] if values else ""
        return ""

    def get_all_urls(self) -> Dict[str, str]:
        """取得所有 URL 配置"""
        urls = {}
        for item in self.url_tree.get_children():
            values = self.url_tree.item(item, "values")
            if len(values) >= 2:
                service_name, url = values[0], values[1]
                urls[service_name] = url
        return urls
