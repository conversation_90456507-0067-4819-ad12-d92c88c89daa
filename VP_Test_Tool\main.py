"""
VP Test Tool
主程式入口點

更新日誌：
- 內存優化：添加內存監控和優化功能，提高大量數據處理時的穩定性
- 網絡恢復：添加網絡連接問題的自動恢復功能，提高程序穩定性
- 用戶界面增強：添加進度條動畫和狀態圖標，提供更好的視覺反饋
- 日誌系統升級：增強日誌記錄系統，添加詳細的操作日誌和錯誤診斷
- 斷點續傳：添加文件下載的斷點續傳功能，提高大文件下載的穩定性
- 資源監控：添加系統資源使用顯示，實時監控內存和 CPU 使用情況
- 批量處理優化：改進批量處理的內存使用和錯誤恢復機制
- 錯誤診斷：添加網絡連接問題的診斷功能，提供更詳細的錯誤信息

舊版更新日誌：
- 操作說明優化：更新資源調整工具中的操作說明，使其更加清晰和符合實際操作流程
- 會員查詢說明改進：明確指出需先選擇資料庫來源，再輸入平台帳號或 VP Member ID
- 批次處理說明完善：添加選擇資料庫來源的步驟說明
- VIP 等級限制調整：VIP 等級輸入範圍從 0-10 調整為 0-6
- 資源調整工具優化：添加 VIP 等級範圍驗證，超出範圍時顯示明確的錯誤訊息
- 使用者介面改進：在 VIP 等級輸入欄位標籤中顯示有效範圍 (0-6)
- 帳號產生器多線程優化：使用 ThreadPoolExecutor 進行並行處理，提高大量帳號生成的效率
- 程式碼優化：解決未使用的變數問題，添加適當的註釋說明
- 錯誤處理增強：改進異常捕獲和處理機制，提供更清晰的錯誤訊息
- 資源管理改進：確保所有文件和資源在使用後正確關閉
- 會員資訊區域增強：所有頁籤中的會員資訊區域現在包含資料庫來源、平台帳號和 VP Member ID 欄位
- Slot Set RNG 頁面優化：改進會員設定和遊戲設定區域的排版，減少空白縮排，調整描述區域寬度
- 帳號產生器頁面改進：進度條現在顯示綠色，並添加百分比顯示
- 資源調整工具更新：金幣更新 API 已更新，支援小數點輸入
"""
import tkinter as tk
import os
import threading  # 用於 on_closing 函數中的線程管理 (第 608 行)
import gc  # 添加 gc 模塊導入
from utils.version import APP_TITLE  # VERSION 在 APP_TITLE 中使用
from views.main_window import MainWindow
from models.member import MemberService
from models.agent import AgentService
from controllers.member_controller import MemberController
from controllers.resource_controller import ResourceController
from controllers.account_controller import AccountController
from controllers.rng_controller import RNGController
from utils.config import Config
try:
    from utils.enhanced_logger import enhanced_logger
except ImportError:
    from utils.logger import setup_logger
    enhanced_logger = setup_logger(__name__)

try:
    from utils.http_client_enhanced import HttpClientEnhanced
    http_client_enhanced = HttpClientEnhanced()
except ImportError:
    from utils.http_client import HttpClient
    http_client_enhanced = HttpClient()

try:
    from utils.memory_monitor import memory_monitor
except ImportError:
    # 如果無法導入 memory_monitor，創建一個模擬對象
    class MemoryMonitorMock:
        def __init__(self):
            self.threshold_mb = 200.0
            self.on_threshold_exceeded = None
            self.on_memory_status = None

        def start_monitoring(self):
            print("內存監控功能未啟用，使用模擬對象")

        def stop_monitoring(self):
            pass

        def get_memory_report(self):
            return "內存監控功能未啟用"

    memory_monitor = MemoryMonitorMock()

try:
    from utils.network_recovery import network_recovery
except ImportError:
    # 如果無法導入 network_recovery，創建一個模擬對象
    class NetworkRecoveryMock:
        def __init__(self):
            self.is_monitoring = False
            self.on_recovery_start = None
            self.on_recovery_success = None
            self.on_recovery_failure = None
            self.on_connection_status = None

        def start_monitoring(self):
            print("網絡恢復功能未啟用，使用模擬對象")

        def stop_monitoring(self):
            pass

        def get_connection_status(self):
            return {"is_connected": True, "last_error": None, "is_monitoring": False}

        def get_diagnostic_report(self):
            return "網絡診斷功能未啟用"

    network_recovery = NetworkRecoveryMock()

# 導入功能檢測器
try:
    from utils.feature_detector import feature_detector
except ImportError:
    # 如果無法導入 feature_detector，創建一個模擬對象
    class FeatureDetectorMock:
        def __init__(self):
            pass

        def detect_features(self):
            return {}

        def get_feature_report(self):
            return "功能檢測未啟用"

        def show_feature_report_dialog(self, parent=None):
            # parent 參數在實際的 FeatureDetector 類中使用，這裡只是模擬
            print("功能檢測未啟用")

    feature_detector = FeatureDetectorMock()
from utils.theme import ThemeManager
from utils.keyboard_shortcuts import KeyboardShortcuts
from views.settings_window import SettingsWindow
try:
    from widgets.resource_monitor import ResourceMonitor
except ImportError:
    ResourceMonitor = None

# 設定日誌
logger = enhanced_logger

# 全局變數，用於存儲 Tkinter 根視窗實例和其他重要對象
_root_window = None
_config = None
_main_window = None
_resource_monitor = None
_is_shutting_down = False  # 標記應用程式是否正在關閉

# 初始化步驟函數
def _init_error_handler():
    """初始化錯誤處理器"""
    try:
        from utils.error_handler import install_global_handler
        # 安裝全局異常處理器
        install_global_handler()
        logger.info("成功初始化錯誤處理器")
    except ImportError:
        logger.warning("無法導入錯誤處理器，使用默認異常處理")
    return None

def _init_system_info():
    """記錄系統信息"""
    logger.log_system_info()
    return None

def _init_memory_monitor():
    """初始化內存監控"""
    memory_monitor.start_monitoring()
    logger.info("成功初始化內存監控")
    return None

def _init_network_monitor():
    """初始化網絡監控"""
    network_recovery.start_monitoring()
    logger.info("成功初始化網絡監控")
    return None

def _init_config():
    """載入設定"""
    global _config
    if _config is None:
        _config = Config()
        logger.info("成功載入設定")
    return _config

def _init_root_window():
    """初始化根視窗"""
    global _root_window, _config

    # 如果根視窗已經存在，檢查它是否仍然有效
    try:
        if _root_window is not None:
            try:
                if _root_window.winfo_exists():
                    return _root_window
            except (tk.TclError, Exception) as e:
                logger.warning(f"根視窗已失效: {e}，將創建新的根視窗")
                _root_window = None
    except Exception as e:
        logger.warning(f"檢查根視窗時發生異常: {e}")
        _root_window = None

    # 確保配置已經載入
    if _config is None:
        _config = _init_config()

    try:
        # 建立根視窗
        _root_window = tk.Tk()
        _root_window.title(APP_TITLE)

        # 設定為最大化視窗，而非全螢幕模式，保留視窗控制鈕和移動功能
        _root_window.state('zoomed')

        # 設定視窗圖示
        try:
            # 嘗試載入應用程式圖示
            icon_path = os.path.join(os.path.dirname(__file__), "assets", "icons", "vp_test_tool.ico")
            if os.path.exists(icon_path):
                _root_window.iconbitmap(icon_path)
                logger.info(f"成功載入應用程式圖示: {icon_path}")
        except Exception as e:
            logger.warning(f"無法載入應用程式圖示: {e}")

        # 初始化主題管理器
        theme_manager = ThemeManager()
        theme_manager.config = _config  # 手動設置 config 屬性
        theme_manager.apply_theme_to_widgets(_root_window)
        logger.info("成功初始化主題管理器")

        return _root_window
    except Exception as e:
        logger.exception(f"初始化根視窗失敗: {e}")
        # 如果創建根視窗失敗，返回 None
        _root_window = None
        return None

def _init_main_window():
    """初始化主視窗"""
    global _root_window, _config, _main_window, _is_shutting_down

    # 如果應用程式正在關閉，不進行初始化
    if _is_shutting_down:
        logger.warning("應用程式正在關閉，不進行主視窗初始化")
        return None

    try:
        # 如果主視窗已經存在，檢查它是否仍然有效
        if _main_window is not None:
            try:
                if _root_window is not None and _root_window.winfo_exists() and _main_window.notebook.winfo_exists():
                    return _main_window
            except (tk.TclError, Exception) as e:
                logger.warning(f"主視窗已失效: {e}，將創建新的主視窗")
                _main_window = None
    except Exception as e:
        logger.warning(f"檢查主視窗時發生異常: {e}")
        _main_window = None

    try:
        # 確保根視窗已經初始化
        if _root_window is None:
            _root_window = _init_root_window()

        # 如果根視窗初始化失敗，返回 None
        if _root_window is None:
            logger.error("無法初始化主視窗：根視窗初始化失敗")
            return None

        # 確保配置已經載入
        if _config is None:
            _config = _init_config()

        # 創建主視窗
        _main_window = MainWindow(_root_window, _config)
        logger.info("成功建立主視窗")
        return _main_window
    except Exception as e:
        logger.exception(f"初始化主視窗失敗: {e}")
        _main_window = None
        return None

def _init_resource_monitor():
    """初始化資源監控元件"""
    global _root_window, _resource_monitor, _is_shutting_down

    # 如果應用程式正在關閉，不進行初始化
    if _is_shutting_down:
        logger.warning("應用程式正在關閉，不進行資源監控元件初始化")
        return None

    try:
        # 如果資源監控元件已經存在，檢查它是否仍然有效
        if _resource_monitor is not None:
            try:
                if _root_window is not None and _root_window.winfo_exists() and _resource_monitor.winfo_exists():
                    return _resource_monitor
            except (tk.TclError, Exception) as e:
                logger.warning(f"資源監控元件已失效: {e}，將創建新的資源監控元件")
                _resource_monitor = None
    except Exception as e:
        logger.warning(f"檢查資源監控元件時發生異常: {e}")
        _resource_monitor = None

    try:
        # 確保根視窗已經初始化
        if _root_window is None:
            _root_window = _init_root_window()

        # 如果根視窗初始化失敗，返回 None
        if _root_window is None:
            logger.error("無法初始化資源監控元件：根視窗初始化失敗")
            return None

        # 檢查 ResourceMonitor 是否可用
        if ResourceMonitor:
            try:
                # 檢查根視窗是否仍然有效
                if not _root_window.winfo_exists():
                    logger.warning("根視窗已失效，無法初始化資源監控元件")
                    return None

                resource_frame = tk.Frame(_root_window)
                resource_frame.pack(side=tk.BOTTOM, fill=tk.X)

                _resource_monitor = ResourceMonitor(
                    resource_frame,
                    update_interval=2.0,
                    show_memory=True,
                    show_cpu=True
                )
                _resource_monitor.pack(side=tk.RIGHT, padx=10, pady=5)
                # 不在這裡啟動監控，而是在主視窗顯示後啟動
                logger.info("成功初始化資源監控元件")
                return _resource_monitor
            except Exception as e:
                logger.warning(f"初始化資源監控元件失敗: {e}")
                _resource_monitor = None
        else:
            logger.warning("資源監控元件未啟用")
        return None
    except Exception as e:
        logger.exception(f"初始化資源監控元件時發生異常: {e}")
        _resource_monitor = None
        return None

def _init_services():
    """初始化服務"""
    # 使用增強版 HTTP 客戶端
    # 啟用網絡恢復和斷點續傳功能
    http_client = http_client_enhanced
    logger.info("成功初始化增強版 HTTP 客戶端")

    # 初始化服務
    member_service = MemberService(http_client)
    agent_service = AgentService(http_client)
    logger.info("成功初始化服務")

    return (http_client, member_service, agent_service)

def _init_controllers(main_window=None, services=None):
    """初始化控制器"""
    # 確保主視窗已經初始化
    if main_window is None:
        main_window = _init_main_window()

    # 確保服務已經初始化
    if services is None:
        services = _init_services()

    http_client, member_service, agent_service = services
    controllers = []

    try:
        member_controller = MemberController(
            main_window.member_panel,
            member_service,
            agent_service
        )
        controllers.append(member_controller)
        logger.info("成功初始化遊戲卡片工具控制器")
    except Exception as e:
        logger.exception(f"初始化遊戲卡片工具控制器失敗: {e}")

    try:
        resource_controller = ResourceController(
            main_window.resource_panel,
            member_service,
            http_client
        )
        controllers.append(resource_controller)
        logger.info("成功初始化資源調整工具控制器")
    except Exception as e:
        logger.exception(f"初始化資源調整工具控制器失敗: {e}")

    try:
        account_controller = AccountController(
            main_window.account_panel,
            member_service,
            agent_service
        )
        controllers.append(account_controller)
        logger.info("成功初始化帳號產生器控制器")
    except Exception as e:
        logger.exception(f"初始化帳號產生器控制器失敗: {e}")

    try:
        rng_controller = RNGController(
            main_window.rng_panel,
            member_service
        )
        controllers.append(rng_controller)
        logger.info("成功初始化 RNG 控制器")
    except Exception as e:
        logger.exception(f"初始化 RNG 控制器失敗: {e}")

    # 初始化 IP 切換工具控制器（如果可用）
    try:
        if hasattr(main_window, 'ip_switcher_panel') and main_window.ip_switcher_panel:
            from controllers.ip_switcher_controller import IPSwitcherController
            ip_switcher_controller = IPSwitcherController(main_window.ip_switcher_panel)
            controllers.append(ip_switcher_controller)
            logger.info("成功初始化 API IP 切換工具控制器")
    except Exception as e:
        logger.exception(f"初始化 API IP 切換工具控制器失敗: {e}")

    return controllers

def _init_features():
    """檢測可用功能"""
    features = feature_detector.detect_features()
    # 檢查是否有 get_available_features 方法
    if hasattr(feature_detector, 'get_available_features') and callable(feature_detector.get_available_features):
        available_features = feature_detector.get_available_features()
        logger.info(f"功能檢測完成，可用功能: {', '.join(available_features)}")
    else:
        # 如果沒有該方法，直接使用 features 字典的鍵
        available_features = list(features.keys()) if isinstance(features, dict) else []
        logger.info(f"功能檢測完成，可用功能: {', '.join(available_features) if available_features else '無'}")
    return features

def _should_pause_for_ip_config():
    """檢查是否需要暫停啟動進行 IP 配置（非阻塞）"""
    try:
        from utils.environment_config import env_config
        import requests
        import threading

        # 取得當前環境配置
        current_env = env_config.get_current_environment()
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})

        # 檢查是否有 API 伺服器配置
        if not api_servers:
            logger.warning("沒有找到 API 伺服器配置")
            return True

        # 快速檢查主要 API 是否可連接
        test_urls = []
        for service, url in api_servers.items():
            if service in ["mysql_operator", "gamebridge"]:  # 檢查關鍵服務
                test_urls.append((service, url))

        if not test_urls:
            logger.warning("沒有找到關鍵 API 服務配置")
            return True

        # 使用線程池進行並行檢測，避免阻塞
        results = {"failed": 0, "total": len(test_urls), "completed": False}

        def test_connection(service, url):
            try:
                response = requests.get(url, timeout=10)
                logger.info(f"API 連接測試成功: {service} - {url}")
                return True
            except Exception as e:
                logger.warning(f"API 連接測試失敗: {service} - {url} - {e}")
                results["failed"] += 1
                return False

        def run_tests():
            import concurrent.futures
            try:
                with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                    futures = [executor.submit(test_connection, service, url) for service, url in test_urls]
                    # 等待所有測試完成，最多等待 30 秒
                    concurrent.futures.wait(futures, timeout=30)
                results["completed"] = True
            except Exception as e:
                logger.error(f"並行連接測試失敗: {e}")
                results["failed"] = len(test_urls)  # 假設全部失敗
                results["completed"] = True

        # 在背景線程中執行測試
        test_thread = threading.Thread(target=run_tests, daemon=True)
        test_thread.start()

        # 等待測試完成，最多等待 35 秒
        test_thread.join(timeout=35)

        if not results["completed"]:
            logger.warning("API 連接測試超時，建議暫停啟動進行配置")
            return True

        # 如果超過一半的關鍵服務無法連接，建議暫停
        if results["failed"] >= results["total"] / 2:
            logger.warning(f"檢測到 {results['failed']}/{results['total']} 個關鍵 API 服務無法連接")
            return True

        logger.info(f"API 連接檢測完成: {results['total'] - results['failed']}/{results['total']} 個服務連接正常")
        return False

    except Exception as e:
        logger.error(f"檢查 IP 配置失敗: {e}")
        # 檢測失敗時，為了安全起見，建議暫停讓用戶檢查配置
        return True

def _pause_startup_for_ip_config(root, main_window):
    """暫停啟動流程，顯示 IP 配置對話框"""
    try:
        import tkinter as tk
        from tkinter import messagebox, ttk

        # 創建 IP 配置對話框
        config_dialog = tk.Toplevel(root)
        config_dialog.title("API IP 配置 - 啟動暫停")
        config_dialog.geometry("900x700")
        config_dialog.transient(root)
        config_dialog.grab_set()

        # 居中顯示
        config_dialog.update_idletasks()
        x = (config_dialog.winfo_screenwidth() // 2) - (config_dialog.winfo_width() // 2)
        y = (config_dialog.winfo_screenheight() // 2) - (config_dialog.winfo_height() // 2)
        config_dialog.geometry(f"+{x}+{y}")

        # 創建說明標籤
        info_frame = ttk.Frame(config_dialog)
        info_frame.pack(fill=tk.X, padx=20, pady=10)

        title_label = ttk.Label(
            info_frame,
            text="⚠️ 檢測到 API 連接問題",
            font=("Microsoft JhengHei UI", 14, "bold"),
            foreground="red"
        )
        title_label.pack()

        info_label = ttk.Label(
            info_frame,
            text="程式啟動已暫停，請先配置正確的 API IP 地址\n"
                 "配置完成後點擊「繼續啟動」按鈕恢復程式執行",
            font=("Microsoft JhengHei UI", 10),
            justify=tk.CENTER
        )
        info_label.pack(pady=10)

        # 嵌入 IP 切換工具面板
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController

        ip_panel = IPSwitcherPanel(config_dialog)
        ip_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # 初始化控制器
        ip_controller = IPSwitcherController(ip_panel)

        # 創建按鈕框架
        button_frame = ttk.Frame(config_dialog)
        button_frame.pack(fill=tk.X, padx=20, pady=10)

        # 結果變數
        result = {"continue": False}

        def test_connection():
            """測試連接"""
            try:
                from utils.environment_config import env_config
                import requests

                current_env = env_config.get_current_environment()
                env_info = env_config.get_environment_info(current_env)
                api_servers = env_info.get("api_servers", {})

                test_results = []
                for service, url in api_servers.items():
                    try:
                        response = requests.get(url, timeout=10)
                        test_results.append(f"✅ {service}: 連接成功")
                    except Exception as e:
                        test_results.append(f"❌ {service}: 連接失敗 - {str(e)[:50]}")

                result_text = "\n".join(test_results)
                messagebox.showinfo("連接測試結果", result_text)

            except Exception as e:
                messagebox.showerror("測試失敗", f"連接測試失敗: {e}")

        def continue_startup():
            """繼續啟動"""
            result["continue"] = True
            config_dialog.destroy()

        def exit_program():
            """退出程式"""
            result["continue"] = False
            config_dialog.destroy()

        # 添加按鈕（使用明顯的樣式）
        test_btn = tk.Button(
            button_frame,
            text="測試連接",
            command=test_connection,
            bg="#01579B",  # 深淺藍色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#0277BD",  # 中淺藍色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        test_btn.pack(side=tk.LEFT, padx=8)

        continue_btn = tk.Button(
            button_frame,
            text="繼續啟動",
            command=continue_startup,
            bg="#1B5E20",  # 深綠色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#2E7D32",  # 中綠色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        continue_btn.pack(side=tk.RIGHT, padx=8)

        exit_btn = tk.Button(
            button_frame,
            text="退出程式",
            command=exit_program,
            bg="#B71C1C",  # 深紅色
            fg="#FFFFFF",  # 白色
            font=("Microsoft JhengHei UI", 10, "bold"),
            activebackground="#D32F2F",  # 中紅色
            activeforeground="#FFFFFF",  # 白色
            relief=tk.RAISED,
            borderwidth=2,
            padx=15,
            pady=8,
            cursor="hand2"
        )
        exit_btn.pack(side=tk.RIGHT, padx=8)

        # 等待對話框關閉
        config_dialog.wait_window()

        # 檢查結果
        if not result["continue"]:
            logger.info("用戶選擇退出程式")
            root.quit()
            return False

        logger.info("用戶選擇繼續啟動，恢復啟動流程")
        return True

    except Exception as e:
        logger.error(f"IP 配置對話框失敗: {e}")
        # 如果對話框失敗，詢問用戶是否繼續
        try:
            result = messagebox.askyesno(
                "啟動確認",
                "檢測到 API 連接問題，是否繼續啟動程式？\n\n"
                "選擇「是」繼續啟動（可能會有功能異常）\n"
                "選擇「否」退出程式"
            )
            if not result:
                root.quit()
            return result
        except:
            return True

def _check_network():
    """檢查網絡連接（非阻塞）"""
    try:
        # 使用較短的超時時間進行網絡檢查，避免阻塞
        import threading
        import time

        result = {"status": None, "error": None}

        def check_network_thread():
            try:
                network_status = network_recovery.get_connection_status()
                result["status"] = network_status
                if not network_status["is_connected"]:
                    logger.warning(f"網絡連接異常: {network_status['last_error']}")
            except Exception as e:
                logger.error(f"網絡檢查失敗: {e}")
                result["error"] = str(e)

        # 在背景線程中執行網絡檢查
        thread = threading.Thread(target=check_network_thread, daemon=True)
        thread.start()

        # 等待最多 15 秒
        thread.join(timeout=15)

        if thread.is_alive():
            logger.warning("網絡檢查超時，跳過網絡狀態檢查")
            return {"is_connected": False, "last_error": "網絡檢查超時"}

        if result["error"]:
            logger.error(f"網絡檢查異常: {result['error']}")
            return {"is_connected": False, "last_error": result["error"]}

        return result["status"] or {"is_connected": False, "last_error": "未知錯誤"}

    except Exception as e:
        logger.error(f"網絡檢查失敗: {e}")
        return {"is_connected": False, "last_error": str(e)}

def main():
    """主程式"""
    try:
        # 確保全局變數已初始化
        global _root_window, _config, _main_window, _resource_monitor, _is_shutting_down
        # 定義初始化步驟
        init_steps = [
            _init_error_handler,
            _init_system_info,
            _init_memory_monitor,
            _init_network_monitor,
            _init_config,
            _init_root_window,
            _init_main_window,
            _init_resource_monitor,
            _init_services,
            _init_controllers,
            _init_features,
            _check_network
        ]

        # 跳過啟動畫面，直接初始化（修復無回應問題）
        logger.info("使用直接初始化模式（跳過啟動畫面）")

        # 重置全局變數
        _root_window = None
        _config = None
        _main_window = None
        _resource_monitor = None
        _is_shutting_down = False

        # 直接執行初始化步驟
        _init_error_handler()
        _init_system_info()
        _init_memory_monitor()
        _init_network_monitor()
        config = _init_config()
        root = _init_root_window()

        # 檢查根視窗是否初始化成功
        if root is None:
            logger.error("根視窗初始化失敗，無法繼續初始化應用程式")
            raise Exception("根視窗初始化失敗")

        main_window = _init_main_window()

        # 檢查主視窗是否初始化成功
        if main_window is None:
            logger.error("主視窗初始化失敗，無法繼續初始化應用程式")
            raise Exception("主視窗初始化失敗")

        resource_monitor = _init_resource_monitor()

        # 以下函數調用雖然未使用返回值，但仍需執行
        _init_services()
        _init_controllers()
        _init_features()

        # 檢查是否需要暫停啟動進行 IP 配置
        if _should_pause_for_ip_config():
            logger.info("檢測到 API IP 配置問題，暫停啟動流程")
            _pause_startup_for_ip_config(root, main_window)

        network_status = _check_network()

        # 啟動資源監控
        if resource_monitor and not _is_shutting_down:
            try:
                resource_monitor.start_monitoring()
                logger.info("成功啟動資源監控")
            except Exception as e:
                logger.warning(f"啟動資源監控時發生異常: {e}")

        # 初始化鍵盤快捷鍵管理器
        try:
            # 確保根視窗有效
            if root and root.winfo_exists():
                shortcuts = KeyboardShortcuts(root)

                # 註冊快捷鍵
                shortcuts.add_shortcut("<F1>", lambda: shortcuts.show_help(root), "顯示快捷鍵說明")
                shortcuts.add_shortcut("<Control-s>", lambda: main_window.show_message("已儲存"), "儲存")
                shortcuts.add_shortcut("<Control-o>", lambda: SettingsWindow(root, config), "開啟設定")
                shortcuts.add_shortcut("<Control-1>", lambda: main_window.notebook.select(0), "切換到資源調整工具")
                shortcuts.add_shortcut("<Control-2>", lambda: main_window.notebook.select(1), "切換到遊戲卡片工具")
                shortcuts.add_shortcut("<Control-3>", lambda: main_window.notebook.select(2), "切換到帳號產生器")
                shortcuts.add_shortcut("<Control-4>", lambda: main_window.notebook.select(3), "切換到 Slot Set RNG")
                shortcuts.add_shortcut("<F5>", lambda: main_window.show_message("已重新整理"), "重新整理")

                # 添加新的快捷鍵
                shortcuts.add_shortcut("<F2>", lambda: _show_network_status(root), "顯示網絡狀態")
                shortcuts.add_shortcut("<F3>", lambda: _show_memory_status(root), "顯示內存狀態")
                shortcuts.add_shortcut("<F4>", lambda: _export_logs(root), "導出日誌")
                shortcuts.add_shortcut("<F6>", lambda: _show_feature_report(root), "顯示功能報告")
                shortcuts.add_shortcut("<F7>", lambda: _check_for_updates(root), "檢查更新")
                shortcuts.add_shortcut("<Control-5>", lambda: main_window.notebook.select(4) if len(main_window.notebook.tabs()) > 4 else None, "切換到功能檢測")

                logger.info("成功初始化鍵盤快捷鍵管理器")
            else:
                logger.warning("根視窗無效，無法初始化鍵盤快捷鍵管理器")
        except Exception as e:
            logger.warning(f"初始化鍵盤快捷鍵管理器失敗: {e}")

        # 檢查網絡連接
        try:
            if not network_status["is_connected"] and main_window and hasattr(main_window, 'show_message') and callable(main_window.show_message):
                main_window.show_message(f"網絡連接異常: {network_status['last_error']}", "warning")
        except Exception as e:
            logger.warning(f"顯示網絡連接異常訊息失敗: {e}")

        # 記錄啟動完成
        logger.info("應用程式啟動完成")

        # 顯示歡迎訊息（確保主視窗有效）
        try:
            if main_window and hasattr(main_window, 'show_message') and callable(main_window.show_message):
                main_window.show_message(f"歡迎使用 {APP_TITLE}", "info")
        except Exception as e:
            logger.warning(f"顯示歡迎訊息失敗: {e}")

        # 添加功能檢測按鈕到主視窗
        try:
            # 確保主視窗有效
            if main_window and hasattr(main_window, 'status_bar') and main_window.status_bar.winfo_exists():
                # 創建功能檢測按鈕
                feature_button = tk.Button(
                    main_window.status_bar,
                    text="功能檢測",
                    command=lambda: _show_feature_report(root)
                )
                feature_button.pack(side=tk.RIGHT, padx=5)

                # 創建更新檢查按鈕
                update_button = tk.Button(
                    main_window.status_bar,
                    text="檢查更新",
                    command=lambda: _check_for_updates(root)
                )
                update_button.pack(side=tk.RIGHT, padx=5)

                logger.info("成功添加功能檢測按鈕")
            else:
                logger.warning("主視窗或狀態欄無效，無法添加功能檢測按鈕")
        except Exception as e:
            logger.warning(f"添加功能檢測按鈕失敗: {e}")

        # 註冊視窗關閉事件處理函數
        def on_closing():
            """視窗關閉事件處理函數"""
            global _is_shutting_down, _root_window, _main_window, _resource_monitor

            try:
                # 設置關閉標記
                _is_shutting_down = True
                logger.info("應用程式正在關閉...")

                # 停止所有執行中的線程
                try:
                    # 停止所有非守護線程
                    import threading
                    for thread in threading.enumerate():
                        if thread != threading.current_thread() and not thread.daemon:
                            logger.info(f"正在停止線程: {thread.name}")
                            # 無法直接停止線程，但可以設置標記讓線程自行結束
                            if hasattr(thread, 'stop') and callable(thread.stop):
                                thread.stop()
                    logger.info("已停止所有非守護線程")

                    # 停止 AccountController 的線程
                    try:
                        # 獲取 AccountController 實例
                        from controllers.account_controller import AccountController
                        for controller in [c for c in gc.get_objects() if isinstance(c, AccountController)]:
                            if hasattr(controller, 'stop') and callable(controller.stop):
                                logger.info(f"正在停止 AccountController 線程")
                                controller.stop()
                        logger.info("已停止所有 AccountController 線程")
                    except Exception as e:
                        logger.warning(f"停止 AccountController 線程時發生異常: {e}")
                except Exception as e:
                    logger.warning(f"停止線程時發生異常: {e}")

                # 停止資源監控
                if _resource_monitor:
                    try:
                        _resource_monitor.stop_monitoring()
                        logger.info("已停止資源監控")
                    except Exception as e:
                        logger.warning(f"停止資源監控時發生異常: {e}")

                # 停止內存監控
                try:
                    memory_monitor.stop_monitoring()
                    logger.info("已停止內存監控")
                except Exception as e:
                    logger.warning(f"停止內存監控時發生異常: {e}")

                # 停止網絡監控
                try:
                    network_recovery.stop_monitoring()
                    logger.info("已停止網絡監控")
                except Exception as e:
                    logger.warning(f"停止網絡監控時發生異常: {e}")

                # 取消所有待處理的 after 調用
                try:
                    if _root_window and _root_window.winfo_exists():
                        # 獲取所有待處理的 after 調用 ID
                        for after_id in _root_window.tk.call('after', 'info'):
                            try:
                                _root_window.after_cancel(after_id)
                            except Exception:
                                pass

                        # 清理所有視圖中的閃爍效果
                        try:
                            # 遍歷所有子元件，清理閃爍效果
                            def clean_blink_jobs(widget):
                                # 清理按鈕閃爍效果
                                if hasattr(widget, 'blink_job') and widget.blink_job:
                                    try:
                                        _root_window.after_cancel(widget.blink_job)
                                        widget.blink_job = None
                                    except Exception:
                                        pass

                                # 清理 ModernButton 的閃爍效果
                                if hasattr(widget, '_flash_count'):
                                    try:
                                        # 重置閃爍狀態
                                        widget._flash_count = 0
                                    except Exception:
                                        pass

                                # 遞迴處理所有子元件
                                for child in widget.winfo_children():
                                    clean_blink_jobs(child)

                            # 從根視窗開始清理
                            clean_blink_jobs(_root_window)
                        except Exception as e:
                            logger.warning(f"清理閃爍效果時發生異常: {e}")

                        logger.info("已取消所有待處理的 after 調用和閃爍效果")
                except Exception as e:
                    logger.warning(f"取消 after 調用時發生異常: {e}")

                # 清理全局變數 (在銷毀視窗前先保存引用)
                _main_window = None
                _resource_monitor = None

                # 關閉視窗 (最後執行，避免後續操作引用已銷毀的視窗)
                if _root_window and _root_window.winfo_exists():
                    try:
                        # 保存引用以便記錄日誌
                        root_window = _root_window
                        # 清空全局變數，避免後續代碼引用已銷毀的視窗
                        _root_window = None
                        # 銷毀視窗
                        root_window.destroy()
                        logger.info("應用程式已關閉")
                    except Exception as e:
                        logger.warning(f"關閉視窗時發生異常: {e}")
                else:
                    # 如果視窗已經無效，直接清空引用
                    _root_window = None

            except Exception as e:
                logger.exception(f"關閉應用程式時發生異常: {e}")
                # 清理全局變數
                _main_window = None
                _resource_monitor = None

                # 強制關閉視窗
                try:
                    if _root_window and _root_window.winfo_exists():
                        # 保存引用以便後續操作
                        root_window = _root_window
                        # 清空全局變數，避免後續代碼引用已銷毀的視窗
                        _root_window = None
                        # 銷毀視窗
                        root_window.destroy()
                    else:
                        # 如果視窗已經無效，直接清空引用
                        _root_window = None
                except:
                    # 如果銷毀失敗，確保引用被清空
                    _root_window = None

        # 確保根視窗有效後再設置關閉事件處理函數
        try:
            if root and root.winfo_exists():
                try:
                    root.protocol("WM_DELETE_WINDOW", on_closing)
                    # 啟動應用程式
                    root.mainloop()
                except Exception as e:
                    logger.exception(f"設置視窗關閉事件處理函數或啟動主循環時發生異常: {e}")
            else:
                logger.error("根視窗無效，無法設置關閉事件處理函數或啟動主循環")
        except Exception as e:
            logger.exception(f"檢查根視窗是否有效時發生異常: {e}")

    except KeyboardInterrupt:
        # 處理鍵盤中斷
        logger.info("程式已被使用者中斷")

        # 停止監控
        memory_monitor.stop_monitoring()
        network_recovery.stop_monitoring()

    except Exception as e:
        # 記錄異常信息
        logger.exception(f"應用程式啟動失敗")

        # 停止監控
        memory_monitor.stop_monitoring()
        network_recovery.stop_monitoring()

        # 顯示錯誤對話框
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror("應用程式啟動失敗", f"應用程式啟動失敗: {e}\n\n請檢查日誌文件以獲取更多信息。")
        except:
            pass

        raise
    finally:
        # 確保在應用程式結束時停止所有監控
        memory_monitor.stop_monitoring()
        network_recovery.stop_monitoring()

def _show_network_status(_):
    """顯示網絡狀態"""
    try:
        # 獲取網絡診斷報告
        report = network_recovery.get_diagnostic_report()

        # 顯示對話框
        import tkinter.messagebox as messagebox
        messagebox.showinfo("網絡狀態", report)

    except Exception as e:
        logger.error(f"顯示網絡狀態失敗: {e}")

def _show_memory_status(_):
    """顯示內存狀態"""
    try:
        # 獲取內存報告
        report = memory_monitor.get_memory_report()

        # 顯示對話框
        import tkinter.messagebox as messagebox
        messagebox.showinfo("內存狀態", report)

    except Exception as e:
        logger.error(f"顯示內存狀態失敗: {e}")

def _export_logs(_):
    """導出日誌"""
    try:
        # 選擇保存路徑
        import tkinter.filedialog as filedialog
        file_path = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON 文件", "*.json"), ("所有文件", "*.*")],
            title="導出日誌"
        )

        if not file_path:
            return

        # 導出日誌
        try:
            # 嘗試使用增強版日誌記錄器
            if hasattr(enhanced_logger, 'export_logs') and callable(enhanced_logger.export_logs):
                if enhanced_logger.export_logs(file_path):
                    # 顯示成功對話框
                    import tkinter.messagebox as messagebox
                    messagebox.showinfo("導出日誌", f"日誌已成功導出到: {file_path}")
                else:
                    # 顯示錯誤對話框
                    import tkinter.messagebox as messagebox
                    messagebox.showerror("導出日誌", "導出日誌失敗，請檢查日誌文件以獲取更多信息。")
            else:
                # 使用基本方法導出日誌
                import json
                with open(file_path, "w", encoding="utf-8") as f:
                    json.dump({"logs": ["基本日誌導出"]}, f, ensure_ascii=False)

                # 顯示成功對話框
                import tkinter.messagebox as messagebox
                messagebox.showinfo("導出日誌", f"基本日誌已導出到: {file_path}")
        except Exception as e:
            # 顯示錯誤對話框
            import tkinter.messagebox as messagebox
            messagebox.showerror("導出日誌", f"導出日誌失敗: {e}")

    except Exception as e:
        logger.error(f"導出日誌失敗: {e}")

        # 顯示錯誤對話框
        import tkinter.messagebox as messagebox
        messagebox.showerror("導出日誌", f"導出日誌失敗: {e}")

def _show_feature_report(parent):
    """顯示功能報告"""
    try:
        # 使用功能檢測器顯示報告
        feature_detector.show_feature_report_dialog(parent)
    except Exception as e:
        logger.error(f"顯示功能報告失敗: {e}")

        # 顯示錯誤對話框
        import tkinter.messagebox as messagebox
        messagebox.showerror("功能報告", f"顯示功能報告失敗: {e}")

def _check_for_updates(parent):
    """檢查更新"""
    try:
        # 導入自動更新模塊
        from utils.auto_updater import check_for_updates

        # 檢查更新
        check_for_updates(parent)
    except ImportError:
        # 顯示錯誤對話框
        import tkinter.messagebox as messagebox
        messagebox.showinfo("檢查更新", "自動更新功能未啟用，請手動檢查更新。")
    except Exception as e:
        logger.error(f"檢查更新失敗: {e}")

        # 顯示錯誤對話框
        import tkinter.messagebox as messagebox
        messagebox.showerror("檢查更新", f"檢查更新失敗: {e}")

if __name__ == "__main__":
    main()
