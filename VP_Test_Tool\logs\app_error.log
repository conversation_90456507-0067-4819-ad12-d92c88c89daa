2025-04-29 20:01:00,912 - ERROR - 40192 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 308, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-29 20:03:33,436 - ERROR - 43556 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 311, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 315, in main
    root.attributes('-alpha', 1.0)  # 設置透明度為 1.0（完全不透明）
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2032, in wm_attributes
    return self.tk.call(args)
           ^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-29 20:07:11,331 - ERROR - 16564 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 311, in main
    root.deiconify()
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2069, in wm_deiconify
    return self.tk.call('wm', 'deiconify', self._w)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 315, in main
    root.attributes('-alpha', 1.0)  # 設置透明度為 1.0（完全不透明）
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2032, in wm_attributes
    return self.tk.call(args)
           ^^^^^^^^^^^^^^^^^^
RuntimeError: main thread is not in main loop
2025-04-30 09:40:09,787 - ERROR - 43504 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 373, in main
    results = splash.show()
              ^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 74, in show
    raise self.init_error
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 177, in _run_next_step
    result = step_func()
             ^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 276, in _init_controllers
    main_window = _init_main_window()
                  ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 225, in _init_main_window
    main_window = MainWindow(_root_window, _config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 396, in main
    root = _init_root_window()
           ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 182, in _init_root_window
    if _root_window is not None and _root_window.winfo_exists():
                                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-04-30 09:46:22,422 - ERROR - 37200 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 373, in main
    # 檢查是否有 get_available_features 方法
            ^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 74, in show
    raise self.init_error
  File "D:\Gitlab\VP_Test_Tool\utils\splash_screen.py", line 177, in _run_next_step
    result = step_func()
             ^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 276, in _init_controllers
    resource_frame = tk.Frame(_root_window)
          ^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 225, in _init_main_window
    _root_window = None
              ^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 396, in main
    _init_system_info,
           ^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\main.py", line 182, in _init_root_window
    try:
         
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-04-30 09:47:52,748 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化主視窗失敗: Theme MyTheme already exists
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 247, in _init_main_window
    main_window = MainWindow(_root_window, _config)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 54, in __init__
    self._init_ui()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 125, in _init_ui
    self._setup_notebook_style()
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 250, in _setup_notebook_style
    style.theme_create("MyTheme", parent="alt", settings={
  File "D:\Program Files\Python311\Lib\tkinter\ttk.py", line 464, in theme_create
    self.tk.call(self._name, "theme", "create", themename,
_tkinter.TclError: Theme MyTheme already exists
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化遊戲卡片工具控制器失敗: 'NoneType' object has no attribute 'member_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 327, in _init_controllers
    main_window.member_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'member_panel'
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化資源調整工具控制器失敗: 'NoneType' object has no attribute 'resource_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 338, in _init_controllers
    main_window.resource_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'resource_panel'
2025-04-30 09:47:52,754 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化帳號產生器控制器失敗: 'NoneType' object has no attribute 'account_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 349, in _init_controllers
    main_window.account_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'account_panel'
2025-04-30 09:47:52,755 - ERROR - 35380 - MainThread - app - enhanced_logger.py:278 - 初始化 RNG 控制器失敗: 'NoneType' object has no attribute 'rng_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 360, in _init_controllers
    main_window.rng_panel,
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'rng_panel'
2025-04-30 10:31:17,054 - ERROR - 35656 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    main_window.show_message(f"歡迎使用 {APP_TITLE}", "info")
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:31:38,199 - ERROR - 18580 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    main_window.show_message(f"歡迎使用 {APP_TITLE}", "info")
  File "D:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:32:06,758 - ERROR - 40588 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 552, in main
    try:
  File "d:\Gitlab\VP_Test_Tool\views\main_window.py", line 236, in show_message
    self.status_bar.config(
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1702, in configure
    return self._configure('configure', cnf, kw)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1692, in _configure
    self.tk.call(_flatten((self._w, cmd)) + self._options(cnf))
_tkinter.TclError: invalid command name ".!label"
2025-04-30 10:38:46,922 - ERROR - 35260 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:39:36,249 - ERROR - 39332 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:42:24,821 - ERROR - 28848 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 654, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 10:45:54,040 - ERROR - 25624 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 681, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 11:28:21,919 - ERROR - 24828 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 694, in main
    # 啟動應用程式
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 11:30:58,013 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,164 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,165 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化主視窗：根視窗初始化失敗
2025-04-30 11:30:58,309 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,309 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化資源監控元件：根視窗初始化失敗
2025-04-30 11:30:58,658 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化根視窗失敗: ThemeManager.__new__() takes 1 positional argument but 2 were given
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 219, in _init_root_window
    theme_manager = ThemeManager(_config)
                    ^^^^^^^^^^^^^^^^^^^^^
TypeError: ThemeManager.__new__() takes 1 positional argument but 2 were given
2025-04-30 11:30:58,659 - ERROR - 35052 - MainThread - app - enhanced_logger.py:256 - 無法初始化主視窗：根視窗初始化失敗
2025-04-30 11:30:58,660 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化遊戲卡片工具控制器失敗: 'NoneType' object has no attribute 'member_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 368, in _init_controllers
    main_window.member_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'member_panel'
2025-04-30 11:30:58,661 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化資源調整工具控制器失敗: 'NoneType' object has no attribute 'resource_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 379, in _init_controllers
    main_window.resource_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'resource_panel'
2025-04-30 11:30:58,661 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化帳號產生器控制器失敗: 'NoneType' object has no attribute 'account_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 390, in _init_controllers
    main_window.account_panel,
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'account_panel'
2025-04-30 11:30:58,662 - ERROR - 35052 - MainThread - app - enhanced_logger.py:278 - 初始化 RNG 控制器失敗: 'NoneType' object has no attribute 'rng_panel'
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 401, in _init_controllers
    main_window.rng_panel,
    ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'NoneType' object has no attribute 'rng_panel'
2025-04-30 11:51:30,630 - ERROR - 11608 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 693, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-04-30 11:59:31,853 - ERROR - 43660 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "D:\Gitlab\VP_Test_Tool\main.py", line 437, in main
    feature_detector = FeatureDetectorMock()
                       ^^^^^^^^^^^^^^^^^^^
NameError: name 'FeatureDetectorMock' is not defined
2025-04-30 13:11:18,295 - ERROR - 36396 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-02 18:57:13,388 - ERROR - 34356 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-07 14:54:45,367 - ERROR - 43132 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-07 16:01:30,785 - ERROR - 55908 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-08 11:36:11,851 - ERROR - 32904 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 704, in main
    root.protocol("WM_DELETE_WINDOW", on_closing)
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 2253, in wm_protocol
    return self.tk.call(
           ^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "wm" command: application has been destroyed
2025-05-08 11:53:32,483 - ERROR - 41808 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 719, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-08 11:53:58,903 - ERROR - 41180 - MainThread - app - enhanced_logger.py:278 - 應用程式啟動失敗
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 719, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:19:23,802 - ERROR - 34544 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-13 14:43:00,038 - ERROR - 22000 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-14 16:33:09,561 - ERROR - 23184 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-14 17:55:42,386 - ERROR - 9976 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 11:19:36,102 - ERROR - 12728 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 12:05:34,199 - ERROR - 42920 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-16 18:14:19,735 - ERROR - 35736 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 750, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
2025-05-28 15:14:25,321 - ERROR - 40092 - MainThread - app - enhanced_logger.py:278 - 檢查根視窗是否有效時發生異常: can't invoke "winfo" command: application has been destroyed
Traceback (most recent call last):
  File "d:\Gitlab\VP_Test_Tool\main.py", line 788, in main
    if root and root.winfo_exists():
                ^^^^^^^^^^^^^^^^^^^
  File "D:\Program Files\Python311\Lib\tkinter\__init__.py", line 1139, in winfo_exists
    self.tk.call('winfo', 'exists', self._w))
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
_tkinter.TclError: can't invoke "winfo" command: application has been destroyed
