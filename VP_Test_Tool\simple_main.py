#!/usr/bin/env python3
"""
簡化版主程式
跳過啟動畫面，直接初始化應用程式
"""

import sys
import os
import logging
import traceback

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def simple_main():
    """簡化版主程式"""
    print("🚀 開始簡化版程式啟動...")
    
    try:
        # 導入必要模組
        print("📦 導入模組...")
        import tkinter as tk
        from utils.config import Config
        from views.main_window import MainWindow
        from utils.enhanced_logger import enhanced_logger
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO)
        logger = enhanced_logger
        
        print("✅ 模組導入完成")
        
        # 初始化配置
        print("⚙️ 初始化配置...")
        config = Config()
        print("✅ 配置初始化完成")
        
        # 創建根視窗
        print("🖼️ 創建根視窗...")
        root = tk.Tk()
        root.title("VP Test Tool - 簡化版")
        root.state('zoomed')  # 最大化視窗
        print("✅ 根視窗創建完成")
        
        # 創建主視窗
        print("🏠 創建主視窗...")
        main_window = MainWindow(root, config)
        print("✅ 主視窗創建完成")
        
        # 初始化控制器
        print("🎮 初始化控制器...")
        
        # 檢查是否有 IP 切換面板
        if hasattr(main_window, 'ip_switcher_panel') and main_window.ip_switcher_panel:
            print("   - 初始化 IP 切換控制器...")
            try:
                from controllers.ip_switcher_controller import IPSwitcherController
                ip_controller = IPSwitcherController(main_window.ip_switcher_panel)
                print("   ✅ IP 切換控制器初始化完成")
            except Exception as e:
                print(f"   ❌ IP 切換控制器初始化失敗: {e}")
                traceback.print_exc()
        else:
            print("   ⚠️ IP 切換面板不存在")
        
        print("✅ 控制器初始化完成")
        
        # 設置關閉事件
        def on_closing():
            print("👋 程式正在關閉...")
            root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        print("🎉 程式啟動完成！")
        print("📝 提示：這是簡化版，部分功能可能不可用")
        
        # 啟動主循環
        root.mainloop()
        
    except KeyboardInterrupt:
        print("\n⚠️ 程式被用戶中斷")
    except Exception as e:
        print(f"❌ 程式啟動失敗: {e}")
        traceback.print_exc()
        
        # 顯示錯誤對話框
        try:
            import tkinter.messagebox as messagebox
            messagebox.showerror("啟動失敗", f"程式啟動失敗:\n{e}")
        except:
            pass

def test_components():
    """測試各個元件"""
    print("🔧 測試各個元件...")
    
    try:
        # 測試環境配置
        print("   - 測試環境配置...")
        from utils.environment_config import env_config
        current_env = env_config.get_current_environment()
        print(f"     當前環境: {current_env}")
        
        # 測試 IP 切換工具
        print("   - 測試 IP 切換工具...")
        from utils.ip_switcher import ip_switcher
        common_ips = ip_switcher.find_common_ips()
        print(f"     常見 IP: {common_ips}")
        
        # 測試 UI 元件
        print("   - 測試 UI 元件...")
        import tkinter as tk
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        
        root = tk.Tk()
        root.withdraw()
        
        panel = IPSwitcherPanel(root)
        controller = IPSwitcherController(panel)
        
        root.destroy()
        print("     ✅ UI 元件測試通過")
        
        print("✅ 所有元件測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 元件測試失敗: {e}")
        traceback.print_exc()
        return False

def main():
    """主函數"""
    print("VP Test Tool - 簡化版啟動器")
    print("=" * 50)
    
    # 先測試元件
    if test_components():
        print("\n🚀 開始啟動簡化版程式...")
        simple_main()
    else:
        print("\n❌ 元件測試失敗，無法啟動程式")
        input("按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
