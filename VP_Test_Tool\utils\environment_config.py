"""環境配置管理器

此模組提供了一個靈活的環境配置管理系統，支援：
1. 多環境配置（開發、測試、生產等）
2. 動態 IP 切換
3. 配置熱重載
4. 配置驗證
5. 配置備份與恢復
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
from .config import Config

logger = logging.getLogger(__name__)

class EnvironmentConfig:
    """環境配置管理器"""

    def __init__(self):
        self.config = Config()
        self.environments_file = "environments.json"
        self.current_environment = "default"
        self.environments = {}
        self.load_environments()

    def load_environments(self):
        """載入環境配置"""
        try:
            if os.path.exists(self.environments_file):
                with open(self.environments_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.environments = data.get("environments", {})
                    self.current_environment = data.get("current_environment", "default")
            else:
                self.create_default_environments()
                self.save_environments()
        except Exception as e:
            logger.error(f"載入環境配置失敗: {e}")
            self.create_default_environments()

    def create_default_environments(self):
        """創建預設環境配置"""
        self.environments = {
            "default": {
                "name": "預設環境",
                "description": "預設的開發環境配置",
                "api_servers": {
                    "mysql_operator": "http://************:5000",
                    "gamebridge": "http://gamebridge:8080",
                    "tokenguard": "https://gp001-qa1-tokenguard.xwautc.online",
                    "lottery": "http://lottery:8080",
                    "simulation": "http://simulationweb-go:8080"
                },
                "created_at": datetime.now().isoformat(),
                "last_used": datetime.now().isoformat()
            },
            "development": {
                "name": "開發環境",
                "description": "本地開發環境配置",
                "api_servers": {
                    "mysql_operator": "http://localhost:5000",
                    "gamebridge": "http://localhost:8080",
                    "tokenguard": "https://dev-tokenguard.example.com",
                    "lottery": "http://localhost:8081",
                    "simulation": "http://localhost:8082"
                },
                "created_at": datetime.now().isoformat(),
                "last_used": None
            },
            "testing": {
                "name": "測試環境",
                "description": "測試環境配置",
                "api_servers": {
                    "mysql_operator": "http://************:5000",
                    "gamebridge": "http://test-gamebridge:8080",
                    "tokenguard": "https://test-tokenguard.xwautc.online",
                    "lottery": "http://test-lottery:8080",
                    "simulation": "http://test-simulation:8080"
                },
                "created_at": datetime.now().isoformat(),
                "last_used": None
            },
            "production": {
                "name": "生產環境",
                "description": "生產環境配置",
                "api_servers": {
                    "mysql_operator": "http://*************:5000",
                    "gamebridge": "http://prod-gamebridge:8080",
                    "tokenguard": "https://prod-tokenguard.xwautc.online",
                    "lottery": "http://prod-lottery:8080",
                    "simulation": "http://prod-simulation:8080"
                },
                "created_at": datetime.now().isoformat(),
                "last_used": None
            }
        }
        self.current_environment = "default"

    def save_environments(self):
        """儲存環境配置"""
        try:
            data = {
                "current_environment": self.current_environment,
                "environments": self.environments,
                "last_updated": datetime.now().isoformat()
            }
            with open(self.environments_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"儲存環境配置失敗: {e}")

    def get_current_environment(self) -> str:
        """取得當前環境名稱"""
        return self.current_environment

    def get_environment_info(self, env_name: str = None) -> Dict[str, Any]:
        """取得環境資訊"""
        env_name = env_name or self.current_environment
        return self.environments.get(env_name, {})

    def get_api_server(self, server_type: str, env_name: str = None) -> str:
        """取得指定類型的 API 伺服器 URL"""
        env_name = env_name or self.current_environment
        env_info = self.get_environment_info(env_name)
        api_servers = env_info.get("api_servers", {})
        return api_servers.get(server_type, "")

    def switch_environment(self, env_name: str) -> bool:
        """切換環境"""
        if env_name not in self.environments:
            logger.error(f"環境 '{env_name}' 不存在")
            return False

        self.current_environment = env_name
        self.environments[env_name]["last_used"] = datetime.now().isoformat()
        self.save_environments()
        logger.info(f"已切換到環境: {env_name}")
        return True

    def add_environment(self, env_name: str, env_config: Dict[str, Any]) -> bool:
        """新增環境"""
        if env_name in self.environments:
            logger.warning(f"環境 '{env_name}' 已存在，將覆蓋現有配置")

        env_config["created_at"] = datetime.now().isoformat()
        env_config["last_used"] = None
        self.environments[env_name] = env_config
        self.save_environments()
        logger.info(f"已新增環境: {env_name}")
        return True

    def remove_environment(self, env_name: str) -> bool:
        """移除環境"""
        if env_name == "default":
            logger.error("無法移除預設環境")
            return False

        if env_name not in self.environments:
            logger.error(f"環境 '{env_name}' 不存在")
            return False

        if env_name == self.current_environment:
            self.switch_environment("default")

        del self.environments[env_name]
        self.save_environments()
        logger.info(f"已移除環境: {env_name}")
        return True

    def list_environments(self) -> List[Dict[str, Any]]:
        """列出所有環境"""
        result = []
        for env_name, env_config in self.environments.items():
            result.append({
                "name": env_name,
                "display_name": env_config.get("name", env_name),
                "description": env_config.get("description", ""),
                "is_current": env_name == self.current_environment,
                "last_used": env_config.get("last_used"),
                "created_at": env_config.get("created_at")
            })
        return result

    def update_api_server(self, server_type: str, new_url: str, env_name: str = None) -> bool:
        """更新指定環境的 API 伺服器 URL"""
        env_name = env_name or self.current_environment
        if env_name not in self.environments:
            logger.error(f"環境 '{env_name}' 不存在")
            return False

        if "api_servers" not in self.environments[env_name]:
            self.environments[env_name]["api_servers"] = {}

        old_url = self.environments[env_name]["api_servers"].get(server_type, "")
        self.environments[env_name]["api_servers"][server_type] = new_url
        self.save_environments()

        logger.info(f"已更新環境 '{env_name}' 的 {server_type} 伺服器: {old_url} -> {new_url}")
        return True

    def backup_environments(self, backup_path: str = None) -> str:
        """備份環境配置"""
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"environments_backup_{timestamp}.json"

        try:
            data = {
                "current_environment": self.current_environment,
                "environments": self.environments,
                "backup_time": datetime.now().isoformat()
            }
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            logger.info(f"環境配置已備份到: {backup_path}")
            return backup_path
        except Exception as e:
            logger.error(f"備份環境配置失敗: {e}")
            return ""

    def restore_environments(self, backup_path: str) -> bool:
        """從備份恢復環境配置"""
        try:
            if not os.path.exists(backup_path):
                logger.error(f"備份檔案不存在: {backup_path}")
                return False

            with open(backup_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.environments = data.get("environments", {})
            self.current_environment = data.get("current_environment", "default")
            self.save_environments()

            logger.info(f"已從備份恢復環境配置: {backup_path}")
            return True
        except Exception as e:
            logger.error(f"恢復環境配置失敗: {e}")
            return False

    def validate_environment(self, env_name: str = None) -> Dict[str, Any]:
        """驗證環境配置"""
        env_name = env_name or self.current_environment
        env_info = self.get_environment_info(env_name)

        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }

        if not env_info:
            validation_result["valid"] = False
            validation_result["errors"].append(f"環境 '{env_name}' 不存在")
            return validation_result

        # 檢查必要的 API 伺服器配置
        required_servers = ["mysql_operator", "gamebridge", "tokenguard", "lottery", "simulation"]
        api_servers = env_info.get("api_servers", {})

        for server in required_servers:
            if server not in api_servers or not api_servers[server]:
                validation_result["warnings"].append(f"缺少 {server} 伺服器配置")

        return validation_result

    def update_environment(self, env_name: str, env_config: Dict[str, Any]) -> bool:
        """更新環境配置"""
        try:
            if env_name not in self.environments:
                logger.error(f"環境 '{env_name}' 不存在")
                return False

            # 保留創建時間
            if "created_at" not in env_config and "created_at" in self.environments[env_name]:
                env_config["created_at"] = self.environments[env_name]["created_at"]

            # 更新最後使用時間
            env_config["last_used"] = datetime.now().isoformat()

            # 更新環境配置
            self.environments[env_name] = env_config
            self.save_environments()

            logger.info(f"已更新環境配置: {env_name}")
            return True

        except Exception as e:
            logger.error(f"更新環境配置失敗: {e}")
            return False

    def reload_config(self) -> bool:
        """重新載入配置文件"""
        try:
            if os.path.exists(self.environments_file):
                with open(self.environments_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.environments = data.get("environments", {})
                self.current_environment = data.get("current_environment", "default")

                logger.info("環境配置已重新載入")
                return True
            else:
                logger.warning("配置文件不存在，使用預設配置")
                self._create_default_environments()
                self.save_environments()
                return True

        except Exception as e:
            logger.error(f"重新載入配置失敗: {e}")
            return False

# 全域環境配置實例
env_config = EnvironmentConfig()
