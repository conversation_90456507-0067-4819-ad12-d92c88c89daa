#!/usr/bin/env python3
"""
直接測試按鈕點擊功能
"""

import sys
import os
import logging

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_button_click_directly():
    """直接測試按鈕點擊"""
    print("=== 直接測試按鈕點擊 ===")
    
    try:
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        import tkinter as tk
        from tkinter import messagebox
        
        # 創建測試環境
        root = tk.Tk()
        root.title("按鈕點擊測試")
        root.geometry("800x600")
        
        # 創建面板和控制器
        panel = IPSwitcherPanel(root)
        panel.pack(fill=tk.BOTH, expand=True)
        
        controller = IPSwitcherController(panel)
        
        # 設置測試數據
        panel.old_ip_var.set("************")
        panel.new_ip_var.set("************")
        
        print(f"舊 IP: {panel.old_ip_var.get()}")
        print(f"新 IP: {panel.new_ip_var.get()}")
        
        # 創建測試結果顯示
        result_frame = tk.Frame(root)
        result_frame.pack(fill=tk.X, padx=10, pady=5)
        
        result_label = tk.Label(
            result_frame,
            text="點擊狀態: 等待測試",
            font=("Microsoft JhengHei UI", 12),
            fg="blue"
        )
        result_label.pack()
        
        # 記錄點擊次數
        click_count = [0]
        
        # 重寫控制器的快速切換方法來記錄點擊
        original_method = controller.quick_switch_ip
        
        def test_quick_switch():
            click_count[0] += 1
            result_label.config(
                text=f"按鈕點擊成功！點擊次數: {click_count[0]}",
                fg="green"
            )
            print(f"✅ 按鈕點擊成功！點擊次數: {click_count[0]}")
            
            # 顯示輸入的 IP
            old_ip = panel.old_ip_var.get()
            new_ip = panel.new_ip_var.get()
            print(f"   舊 IP: {old_ip}")
            print(f"   新 IP: {new_ip}")
            
            # 可選：調用原始方法（會彈出確認對話框）
            # original_method()
        
        # 替換方法
        controller.quick_switch_ip = test_quick_switch
        panel.quick_switch_ip = test_quick_switch
        panel.switch_btn.config(command=test_quick_switch)
        
        # 添加說明和測試按鈕
        info_frame = tk.Frame(root)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_label = tk.Label(
            info_frame,
            text="測試說明：點擊「快速切換」按鈕測試功能是否正常",
            font=("Microsoft JhengHei UI", 10)
        )
        info_label.pack()
        
        # 添加額外的測試按鈕
        test_btn = tk.Button(
            info_frame,
            text="直接測試方法",
            command=test_quick_switch,
            bg="lightblue",
            font=("Microsoft JhengHei UI", 10)
        )
        test_btn.pack(pady=5)
        
        # 添加關閉按鈕
        close_btn = tk.Button(
            info_frame,
            text="關閉測試",
            command=root.destroy,
            bg="red",
            fg="white",
            font=("Microsoft JhengHei UI", 10)
        )
        close_btn.pack(pady=5)
        
        print("✅ 測試視窗已打開")
        print("請點擊「快速切換」按鈕或「直接測試方法」按鈕")
        
        # 啟動主循環
        root.mainloop()
        
        if click_count[0] > 0:
            print(f"✅ 測試成功！總點擊次數: {click_count[0]}")
            return True
        else:
            print("❌ 測試失敗！沒有檢測到按鈕點擊")
            return False
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_controller_method_directly():
    """直接測試控制器方法"""
    print("\n=== 直接測試控制器方法 ===")
    
    try:
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        import tkinter as tk
        
        # 創建測試環境
        root = tk.Tk()
        root.withdraw()  # 隱藏主視窗
        
        # 創建面板和控制器
        panel = IPSwitcherPanel(root)
        controller = IPSwitcherController(panel)
        
        # 設置測試數據
        panel.old_ip_var.set("************")
        panel.new_ip_var.set("************")
        
        print(f"舊 IP: {panel.old_ip_var.get()}")
        print(f"新 IP: {panel.new_ip_var.get()}")
        
        # 檢查方法是否存在
        if hasattr(controller, 'quick_switch_ip'):
            print("✅ 控制器有 quick_switch_ip 方法")
            
            # 檢查方法是否可調用
            if callable(controller.quick_switch_ip):
                print("✅ quick_switch_ip 方法可調用")
                
                # 測試方法調用（不實際執行，避免彈出對話框）
                try:
                    # 這裡我們只測試方法的前半部分邏輯
                    old_ip = panel.old_ip_var.get().strip()
                    new_ip = panel.new_ip_var.get().strip()
                    
                    if not old_ip:
                        print("❌ 舊 IP 為空")
                        return False
                    
                    if not new_ip:
                        print("❌ 新 IP 為空")
                        return False
                    
                    if old_ip == new_ip:
                        print("❌ 新舊 IP 相同")
                        return False
                    
                    print("✅ 輸入驗證通過")
                    print("✅ 控制器方法邏輯正常")
                    
                except Exception as e:
                    print(f"❌ 方法調用失敗: {e}")
                    return False
            else:
                print("❌ quick_switch_ip 方法不可調用")
                return False
        else:
            print("❌ 控制器沒有 quick_switch_ip 方法")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("按鈕點擊功能直接測試開始")
    print("=" * 50)
    
    # 設定日誌級別
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_controller_method_directly,
        test_button_click_directly  # 這個會打開測試視窗
    ]
    
    passed = 0
    total = len(tests)
    
    for i, test in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test.__name__}")
        try:
            if test():
                passed += 1
                print(f"✅ 測試 {test.__name__} 通過")
            else:
                print(f"❌ 測試 {test.__name__} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test.__name__} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 按鈕點擊功能正常！")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
