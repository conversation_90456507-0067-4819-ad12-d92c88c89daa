#!/usr/bin/env python3
"""
不修改路徑的測試
"""

print("開始測試...")

try:
    print("測試 1: 基本功能")
    import sys
    print(f"Python 路徑: {len(sys.path)} 個路徑")
    
    print("測試 2: 當前目錄")
    import os
    print(f"當前目錄: {os.getcwd()}")
    
    print("測試 3: 檢查文件")
    if os.path.exists("main.py"):
        print("main.py 存在")
    else:
        print("main.py 不存在")
    
    if os.path.exists("utils"):
        print("utils 目錄存在")
    else:
        print("utils 目錄不存在")
    
    print("測試 4: 嘗試導入本地模組")
    try:
        import utils.config
        print("utils.config 導入成功")
    except Exception as e:
        print(f"utils.config 導入失敗: {e}")
    
    print("測試完成")
    
except Exception as e:
    print(f"測試失敗: {e}")
    import traceback
    traceback.print_exc()

print("腳本結束")
