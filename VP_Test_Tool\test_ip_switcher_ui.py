#!/usr/bin/env python3
"""
API IP 切換工具 UI 測試腳本
用於測試快速 IP 切換功能的問題
"""

import sys
import os
import logging

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ip_switcher_ui():
    """測試 IP 切換工具 UI 功能"""
    print("=== 測試 IP 切換工具 UI 功能 ===")
    
    try:
        # 導入必要模組
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        import tkinter as tk
        from tkinter import ttk
        
        print("✅ 成功導入 UI 模組")
        
        # 創建測試視窗
        root = tk.Tk()
        root.title("IP 切換工具測試")
        root.geometry("800x600")
        
        # 創建 IP 切換面板
        ip_panel = IPSwitcherPanel(root)
        ip_panel.pack(fill=tk.BOTH, expand=True)
        
        print("✅ 成功創建 IP 切換面板")
        
        # 檢查快速切換按鈕的命令
        print(f"快速切換按鈕命令: {ip_panel.switch_btn['command']}")
        print(f"快速切換方法: {ip_panel.quick_switch_ip}")
        
        # 創建控制器
        controller = IPSwitcherController(ip_panel)
        print("✅ 成功創建控制器")
        
        # 檢查綁定後的方法
        print(f"綁定後的快速切換方法: {ip_panel.quick_switch_ip}")
        print(f"控制器的快速切換方法: {controller.quick_switch_ip}")
        
        # 測試方法調用
        print("\n=== 測試方法調用 ===")
        
        # 設置測試數據
        ip_panel.old_ip_var.set("************")
        ip_panel.new_ip_var.set("************")
        
        print(f"舊 IP: {ip_panel.old_ip_var.get()}")
        print(f"新 IP: {ip_panel.new_ip_var.get()}")
        
        # 測試按鈕點擊
        def test_button_click():
            print("測試按鈕點擊...")
            try:
                ip_panel.quick_switch_ip()
                print("✅ 按鈕點擊成功")
            except Exception as e:
                print(f"❌ 按鈕點擊失敗: {e}")
                import traceback
                traceback.print_exc()
        
        # 添加測試按鈕
        test_frame = ttk.Frame(root)
        test_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(test_frame, text="測試快速切換", command=test_button_click).pack(side=tk.LEFT, padx=5)
        
        # 添加關閉按鈕
        ttk.Button(test_frame, text="關閉", command=root.destroy).pack(side=tk.RIGHT, padx=5)
        
        print("\n✅ 測試視窗已準備就緒")
        print("請在視窗中測試快速切換功能")
        
        # 啟動主循環
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ UI 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_controller_binding():
    """測試控制器綁定"""
    print("\n=== 測試控制器綁定 ===")
    
    try:
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        import tkinter as tk
        
        # 創建簡單的測試環境
        root = tk.Tk()
        root.withdraw()  # 隱藏主視窗
        
        # 創建面板
        panel = IPSwitcherPanel(root)
        print(f"面板創建前的方法: {panel.quick_switch_ip}")
        
        # 創建控制器
        controller = IPSwitcherController(panel)
        print(f"控制器創建後的方法: {panel.quick_switch_ip}")
        
        # 檢查是否正確綁定
        if panel.quick_switch_ip == controller.quick_switch_ip:
            print("✅ 控制器方法綁定成功")
        else:
            print("❌ 控制器方法綁定失敗")
            print(f"面板方法: {panel.quick_switch_ip}")
            print(f"控制器方法: {controller.quick_switch_ip}")
        
        # 檢查按鈕命令
        button_command = panel.switch_btn['command']
        print(f"按鈕命令: {button_command}")
        
        if button_command == panel.quick_switch_ip:
            print("✅ 按鈕命令綁定正確")
        else:
            print("❌ 按鈕命令綁定錯誤")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 控制器綁定測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ip_switcher_logic():
    """測試 IP 切換邏輯"""
    print("\n=== 測試 IP 切換邏輯 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        from utils.environment_config import env_config
        
        # 測試取得當前環境
        current_env = env_config.get_current_environment()
        print(f"當前環境: {current_env}")
        
        # 測試取得環境資訊
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        print(f"API 伺服器數量: {len(api_servers)}")
        
        # 測試找出常見 IP
        common_ips = ip_switcher.find_common_ips()
        print(f"常見 IP: {common_ips}")
        
        # 測試 IP 切換邏輯（不實際執行）
        test_old_ip = "************"
        test_new_ip = "************"
        
        print(f"測試 IP 切換: {test_old_ip} -> {test_new_ip}")
        
        # 檢查是否有包含測試 IP 的配置
        matching_servers = []
        for server_type, url in api_servers.items():
            if test_old_ip in url:
                matching_servers.append(server_type)
        
        print(f"包含 '{test_old_ip}' 的伺服器: {matching_servers}")
        
        if matching_servers:
            print("✅ IP 切換邏輯測試通過")
        else:
            print(f"⚠️ 當前配置中沒有包含 '{test_old_ip}' 的伺服器")
        
        return True
        
    except Exception as e:
        print(f"❌ IP 切換邏輯測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("API IP 切換工具 UI 測試開始")
    print("=" * 50)
    
    # 設定日誌級別
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_controller_binding,
        test_ip_switcher_logic,
        test_ip_switcher_ui  # 這個測試會打開 UI 視窗
    ]
    
    passed = 0
    total = len(tests)
    
    for i, test in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test.__name__}")
        try:
            if test():
                passed += 1
                print(f"✅ 測試 {test.__name__} 通過")
            else:
                print(f"❌ 測試 {test.__name__} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test.__name__} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
