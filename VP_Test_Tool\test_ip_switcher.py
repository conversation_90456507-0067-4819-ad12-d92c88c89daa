#!/usr/bin/env python3
"""
API IP 切換工具測試腳本
用於測試 IP 切換工具的各項功能
"""

import sys
import os
import logging

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ip_switcher_import():
    """測試 IP 切換工具模組導入"""
    print("=== 測試模組導入 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        print("✅ IP 切換工具模組導入成功")
        return True
    except ImportError as e:
        print(f"❌ IP 切換工具模組導入失敗: {e}")
        return False

def test_environment_config():
    """測試環境配置"""
    print("\n=== 測試環境配置 ===")
    
    try:
        from utils.environment_config import env_config
        print("✅ 環境配置模組導入成功")
        
        # 測試取得當前環境
        current_env = env_config.get_current_environment()
        print(f"✅ 當前環境: {current_env}")
        
        # 測試取得環境資訊
        env_info = env_config.get_environment_info(current_env)
        print(f"✅ 環境資訊: {len(env_info)} 項配置")
        
        return True
    except Exception as e:
        print(f"❌ 環境配置測試失敗: {e}")
        return False

def test_ip_switcher_functions():
    """測試 IP 切換工具功能"""
    print("\n=== 測試 IP 切換工具功能 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        
        # 測試取得模板
        templates = ip_switcher.get_templates()
        print(f"✅ 取得模板: {len(templates)} 個模板")
        for name, template in templates.items():
            print(f"   - {name}: {template.get('description', '無描述')}")
        
        # 測試取得歷史記錄
        history = ip_switcher.get_history()
        print(f"✅ 取得歷史記錄: {len(history)} 筆記錄")
        
        # 測試找出常見 IP
        common_ips = ip_switcher.find_common_ips()
        print(f"✅ 常見 IP: {common_ips}")
        
        return True
    except Exception as e:
        print(f"❌ IP 切換工具功能測試失敗: {e}")
        return False

def test_controller_import():
    """測試控制器導入"""
    print("\n=== 測試控制器導入 ===")
    
    try:
        from controllers.ip_switcher_controller import IPSwitcherController
        print("✅ IP 切換工具控制器導入成功")
        return True
    except ImportError as e:
        print(f"❌ IP 切換工具控制器導入失敗: {e}")
        return False

def test_panel_import():
    """測試面板導入"""
    print("\n=== 測試面板導入 ===")
    
    try:
        from views.ip_switcher_panel import IPSwitcherPanel
        print("✅ IP 切換工具面板導入成功")
        return True
    except ImportError as e:
        print(f"❌ IP 切換工具面板導入失敗: {e}")
        return False

def test_template_operations():
    """測試模板操作"""
    print("\n=== 測試模板操作 ===")
    
    try:
        from utils.ip_switcher import ip_switcher
        
        # 測試取得特定模板
        template_name = "常用開發環境"
        template = ip_switcher.get_template(template_name)
        if template:
            print(f"✅ 取得模板 '{template_name}' 成功")
            print(f"   配置項目: {list(template.keys())}")
        else:
            print(f"❌ 取得模板 '{template_name}' 失敗")
            return False
        
        return True
    except Exception as e:
        print(f"❌ 模板操作測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print("API IP 切換工具測試開始")
    print("=" * 50)
    
    # 設定日誌級別
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_ip_switcher_import,
        test_environment_config,
        test_controller_import,
        test_panel_import,
        test_ip_switcher_functions,
        test_template_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 測試執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！API IP 切換工具運作正常。")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查相關模組。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
