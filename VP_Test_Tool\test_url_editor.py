#!/usr/bin/env python3
"""
URL 編輯功能測試腳本
"""

import sys
import os
import logging

# 添加專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_url_editor_ui():
    """測試 URL 編輯器 UI"""
    print("=== 測試 URL 編輯器 UI ===")
    
    try:
        from views.ip_switcher_panel import IPSwitcherPanel
        from controllers.ip_switcher_controller import IPSwitcherController
        import tkinter as tk
        
        # 創建測試視窗
        root = tk.Tk()
        root.title("URL 編輯器測試")
        root.geometry("1200x800")
        
        # 創建面板和控制器
        panel = IPSwitcherPanel(root)
        panel.pack(fill=tk.BOTH, expand=True)
        
        controller = IPSwitcherController(panel)
        
        print("✅ 成功創建 URL 編輯器界面")
        
        # 檢查 URL 編輯相關元件
        components_to_check = [
            "url_tree",
            "edit_url_btn",
            "save_urls_btn", 
            "reset_urls_btn",
            "reload_urls_btn"
        ]
        
        all_present = True
        for component in components_to_check:
            if hasattr(panel, component):
                print(f"✅ {component} 元件存在")
            else:
                print(f"❌ {component} 元件缺失")
                all_present = False
        
        if all_present:
            print("✅ 所有 URL 編輯元件都存在")
        else:
            print("❌ 部分 URL 編輯元件缺失")
        
        # 檢查控制器方法
        controller_methods = [
            "edit_url",
            "save_urls",
            "reset_urls", 
            "reload_urls"
        ]
        
        all_methods_present = True
        for method in controller_methods:
            if hasattr(controller, method):
                print(f"✅ 控制器方法 {method} 存在")
            else:
                print(f"❌ 控制器方法 {method} 缺失")
                all_methods_present = False
        
        if all_methods_present:
            print("✅ 所有控制器方法都存在")
        else:
            print("❌ 部分控制器方法缺失")
        
        # 添加測試說明
        info_frame = tk.Frame(root)
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        info_label = tk.Label(
            info_frame,
            text="URL 編輯器測試說明：\n"
                 "1. 右側應該有 URL 配置編輯區域\n"
                 "2. 可以雙擊 URL 項目進行編輯\n"
                 "3. 可以使用編輯、保存、重置、重新載入按鈕\n"
                 "4. 修改後會自動更新配置文件",
            font=("Microsoft JhengHei UI", 10),
            justify=tk.LEFT
        )
        info_label.pack()
        
        # 添加關閉按鈕
        close_btn = tk.Button(
            info_frame,
            text="關閉測試",
            command=root.destroy,
            bg="red",
            fg="white",
            font=("Microsoft JhengHei UI", 10)
        )
        close_btn.pack(pady=5)
        
        print("✅ 測試視窗已準備就緒")
        print("請在視窗中測試 URL 編輯功能")
        
        # 啟動主循環
        root.mainloop()
        
        return all_present and all_methods_present
        
    except Exception as e:
        print(f"❌ UI 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_url_functionality():
    """測試 URL 功能邏輯"""
    print("\n=== 測試 URL 功能邏輯 ===")
    
    try:
        from utils.environment_config import env_config
        
        # 測試取得當前環境
        current_env = env_config.get_current_environment()
        print(f"當前環境: {current_env}")
        
        # 測試取得環境資訊
        env_info = env_config.get_environment_info(current_env)
        api_servers = env_info.get("api_servers", {})
        print(f"API 伺服器數量: {len(api_servers)}")
        
        # 顯示當前 URL 配置
        print("當前 URL 配置:")
        for service, url in api_servers.items():
            print(f"  {service}: {url}")
        
        # 檢查是否包含指定的服務
        expected_services = [
            "mysql_operator",
            "gamebridge", 
            "tokenguard",
            "lottery",
            "simulation"
        ]
        
        all_services_present = True
        for service in expected_services:
            if service in api_servers:
                print(f"✅ 服務 {service} 存在")
            else:
                print(f"❌ 服務 {service} 缺失")
                all_services_present = False
        
        if all_services_present:
            print("✅ 所有預期服務都存在")
        else:
            print("❌ 部分預期服務缺失")
        
        return all_services_present
        
    except Exception as e:
        print(f"❌ 功能測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_environment_config():
    """測試環境配置功能"""
    print("\n=== 測試環境配置功能 ===")
    
    try:
        from utils.environment_config import env_config
        
        # 測試環境配置方法
        methods_to_check = [
            "get_current_environment",
            "get_environment_info",
            "update_environment",
            "reload_config"
        ]
        
        all_methods_exist = True
        for method in methods_to_check:
            if hasattr(env_config, method):
                print(f"✅ 環境配置方法 {method} 存在")
            else:
                print(f"❌ 環境配置方法 {method} 缺失")
                all_methods_exist = False
        
        if all_methods_exist:
            print("✅ 所有環境配置方法都存在")
        else:
            print("❌ 部分環境配置方法缺失")
        
        # 測試配置文件是否存在
        import os
        config_file = "environments.json"
        if os.path.exists(config_file):
            print(f"✅ 配置文件 {config_file} 存在")
        else:
            print(f"❌ 配置文件 {config_file} 不存在")
            all_methods_exist = False
        
        return all_methods_exist
        
    except Exception as e:
        print(f"❌ 環境配置測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主測試函數"""
    print("URL 編輯功能測試開始")
    print("=" * 50)
    
    # 設定日誌級別
    logging.basicConfig(level=logging.WARNING)
    
    tests = [
        test_environment_config,
        test_url_functionality,
        test_url_editor_ui  # 這個會打開測試視窗
    ]
    
    passed = 0
    total = len(tests)
    
    for i, test in enumerate(tests):
        print(f"\n執行測試 {i+1}/{total}: {test.__name__}")
        try:
            if test():
                passed += 1
                print(f"✅ 測試 {test.__name__} 通過")
            else:
                print(f"❌ 測試 {test.__name__} 失敗")
        except Exception as e:
            print(f"❌ 測試 {test.__name__} 執行異常: {e}")
    
    print("\n" + "=" * 50)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 URL 編輯功能測試通過！")
        return 0
    else:
        print("⚠️ 部分測試失敗，請檢查相關功能。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
